// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using LangChain.Providers.OpenAI;
using LangChain.Providers;
using LangChain.Databases.InMemory;

namespace Admin.NET.Core.Service;

/// <summary>
/// AI聊天服务 🧩
/// </summary>
[ApiDescriptionSettings(Order = 380)]
public class SysAiChatService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<SysAiChat> _sysAiChatRep;
    private readonly SqlSugarRepository<SysAiChatMessage> _sysAiChatMessageRep;
    private readonly SqlSugarRepository<SysAiKnowledge> _sysAiKnowledgeRep;
    private readonly UserManager _userManager;
    private readonly SysCacheService _sysCacheService;
    private readonly IHubContext<OnlineUserHub, IOnlineUserHub> _chatHubContext;
    private readonly IOptions<AiChatOptions> _aiChatOptions;
    private readonly ILogger<SysAiChatService> _logger;
    private readonly AiChatEngine _aiChatEngine;
    private readonly SysAiKnowledgeService _aiKnowledgeService;

    public SysAiChatService(
        SqlSugarRepository<SysAiChat> sysAiChatRep,
        SqlSugarRepository<SysAiChatMessage> sysAiChatMessageRep,
        SqlSugarRepository<SysAiKnowledge> sysAiKnowledgeRep,
        UserManager userManager,
        SysCacheService sysCacheService,
        IHubContext<OnlineUserHub, IOnlineUserHub> chatHubContext,
        IOptions<AiChatOptions> aiChatOptions,
        ILogger<SysAiChatService> logger,
        AiChatEngine aiChatEngine,
        SysAiKnowledgeService aiKnowledgeService)
    {
        _sysAiChatRep = sysAiChatRep;
        _sysAiChatMessageRep = sysAiChatMessageRep;
        _sysAiKnowledgeRep = sysAiKnowledgeRep;
        _userManager = userManager;
        _sysCacheService = sysCacheService;
        _chatHubContext = chatHubContext;
        _aiChatOptions = aiChatOptions;
        _logger = logger;
        _aiChatEngine = aiChatEngine;
        _aiKnowledgeService = aiKnowledgeService;
    }

    /// <summary>
    /// 获取AI聊天分页列表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取AI聊天分页列表")]
    public async Task<SqlSugarPagedList<SysAiChat>> GetAiChatPage(PageAiChatInput input)
    {
        return await _sysAiChatRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Title), u => u.Title.Contains(input.Title))
            .WhereIF(input.UserId.HasValue, u => u.UserId == input.UserId)
            .WhereIF(input.Status.HasValue, u => u.Status == (int)input.Status)
            .WhereIF(input.StartTime.HasValue, u => u.CreateTime >= input.StartTime)
            .WhereIF(input.EndTime.HasValue, u => u.CreateTime <= input.EndTime)
            .OrderByDescending(u => u.LastMessageTime)
            .ThenByDescending(u => u.CreateTime)
            .ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取用户的聊天列表 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取用户的聊天列表")]
    public async Task<List<SysAiChat>> GetUserChatList()
    {
        var userId = _userManager.UserId;
        return await _sysAiChatRep.AsQueryable()
            .Where(u => u.UserId == userId && u.Status != (int)AiChatStatusEnum.Deleted)
            .OrderByDescending(u => u.LastMessageTime)
            .ThenByDescending(u => u.CreateTime)
            .Take(50) // 限制返回最近50个会话
            .ToListAsync();
    }

    /// <summary>
    /// 增加AI聊天 📢
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加AI聊天")]
    public async Task<long> AddAiChat(AddAiChatInput input)
    {
        var entity = input.Adapt<SysAiChat>();
        entity.UserId = _userManager.UserId;
        entity.UserName = _userManager.RealName;
        entity.TenantId = _userManager.TenantId;
        
        var newEntity = await _sysAiChatRep.InsertReturnEntityAsync(entity);
        return newEntity.Id;
    }

    /// <summary>
    /// 更新AI聊天 📢
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新AI聊天")]
    public async Task UpdateAiChat(UpdateAiChatInput input)
    {
        var entity = input.Adapt<SysAiChat>();
        await _sysAiChatRep.UpdateAsync(entity);
    }

    /// <summary>
    /// 删除AI聊天 📢
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [DisplayName("删除AI聊天")]
    public async Task DeleteAiChat(DeleteAiChatInput input)
    {
        var entity = await _sysAiChatRep.GetByIdAsync(input.Id);
        if (entity == null)
            throw Oops.Oh("聊天会话不存在");

        // 检查权限：只能删除自己的聊天
        if (entity.UserId != _userManager.UserId)
            throw Oops.Oh("无权限删除此聊天会话");

        // 软删除：更新状态为已删除
        entity.Status = (int)AiChatStatusEnum.Deleted;
        await _sysAiChatRep.UpdateAsync(entity);

        // 同时删除相关消息
        await _sysAiChatMessageRep.UpdateAsync(
            u => u.ChatId == input.Id,
            u => new SysAiChatMessage { Status = 1 });
    }

    /// <summary>
    /// 获取AI聊天详情 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取AI聊天详情")]
    public async Task<SysAiChat> GetAiChat([FromQuery] BaseIdInput input)
    {
        var entity = await _sysAiChatRep.GetByIdAsync(input.Id);
        if (entity == null)
            throw Oops.Oh("聊天会话不存在");

        // 检查权限：只能查看自己的聊天
        if (entity.UserId != _userManager.UserId)
            throw Oops.Oh("无权限查看此聊天会话");

        return entity;
    }

    /// <summary>
    /// 发送消息并获取AI回复 📢
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "SendMessage"), HttpPost]
    [DisplayName("发送消息并获取AI回复")]
    public async Task<AiReplyOutput> SendMessage(SendMessageInput input)
    {
        var startTime = DateTime.Now;
        var userId = _userManager.UserId;
        var userName = _userManager.RealName;

        try
        {
            // 内容过滤
            if (_aiChatOptions.Value.EnableContentFilter)
            {
                await ValidateMessageContent(input.Content);
            }

            // 获取或创建聊天会话
            var chat = await GetOrCreateChat(input, userId, userName);

            // 保存用户消息
            var userMessage = await SaveUserMessage(chat.Id, input.Content, userId, userName);

            // 获取聊天历史
            var chatHistory = await GetChatHistory(chat.Id);

            // 搜索相关知识库
            List<KnowledgeSearchResult> relatedKnowledge = new();
            if (input.UseKnowledge && _aiChatOptions.Value.EnableKnowledgeRetrieval)
            {
                relatedKnowledge = await SearchKnowledge(input.Content);
            }

            // 构建AI提示词
            var prompt = await BuildPrompt(input.Content, chatHistory, relatedKnowledge);

            // 调用AI获取回复
            var aiResponse = await GetAiResponse(prompt, input.ModelName);

            // 保存AI回复
            var aiMessage = await SaveAiMessage(chat.Id, aiResponse.Content,
                aiResponse.ModelName, aiResponse.ProcessTime, aiResponse.TokenUsage, userId, userName);

            // 更新会话信息
            await UpdateChatInfo(chat.Id, userMessage.Sequence + 1);

            // 通过SignalR发送实时消息
            await SendRealtimeMessage(chat.Id, aiMessage);

            var result = new AiReplyOutput
            {
                ChatId = chat.Id,
                MessageId = aiMessage.Id,
                Content = aiResponse.Content,
                ModelName = aiResponse.ModelName,
                ProcessTime = (int)(DateTime.Now - startTime).TotalMilliseconds,
                TokenUsage = aiResponse.TokenUsage,
                UsedKnowledge = relatedKnowledge.Any(),
                RelatedKnowledge = relatedKnowledge.Select(k => k.Title).ToList()
            };

            // 记录日志
            if (_aiChatOptions.Value.EnableLogging)
            {
                _logger.LogInformation("AI聊天完成 - 用户:{UserId}, 会话:{ChatId}, 处理时间:{ProcessTime}ms",
                    userId, chat.Id, result.ProcessTime);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AI聊天处理失败 - 用户:{UserId}, 消息:{Message}", userId, input.Content);
            throw;
        }
    }

    /// <summary>
    /// 获取聊天历史 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取聊天历史")]
    public async Task<SqlSugarPagedList<ChatHistoryOutput>> GetChatHistory(GetChatHistoryInput input)
    {
        // 检查会话权限
        var chat = await _sysAiChatRep.GetByIdAsync(input.ChatId);
        if (chat == null || chat.UserId != _userManager.UserId)
            throw Oops.Oh("无权限查看此聊天历史");

        var query = _sysAiChatMessageRep.AsQueryable()
            .Where(u => u.ChatId == input.ChatId && u.Status == 0);

        if (!input.IncludeSystemMessages)
        {
            query = query.Where(u => u.Role != "system");
        }

        var result = await query
            .OrderBy(u => u.Sequence)
            .Select(u => new ChatHistoryOutput
            {
                Id = u.Id,
                Role = u.Role,
                Content = u.Content,
                Sequence = u.Sequence,
                UserName = u.UserName,
                ModelName = u.ModelName,
                ProcessTime = u.ProcessTime,
                TokenUsage = u.TokenUsage,
                CreateTime = u.CreateTime
            })
            .ToPagedListAsync(input.Page, input.PageSize);

        return result;
    }

    #region 私有方法

    /// <summary>
    /// 验证消息内容
    /// </summary>
    private async Task ValidateMessageContent(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
            throw Oops.Oh("消息内容不能为空");

        if (content.Length > 4000)
            throw Oops.Oh("消息内容过长，请控制在4000字符以内");

        // 敏感词检测
        var sensitiveWords = _aiChatOptions.Value.SensitiveWords;
        if (sensitiveWords?.Any() == true)
        {
            foreach (var word in sensitiveWords)
            {
                if (content.Contains(word, StringComparison.OrdinalIgnoreCase))
                {
                    throw Oops.Oh($"消息包含敏感词：{word}");
                }
            }
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取或创建聊天会话
    /// </summary>
    private async Task<SysAiChat> GetOrCreateChat(SendMessageInput input, long userId, string userName)
    {
        if (input.ChatId.HasValue)
        {
            var existingChat = await _sysAiChatRep.GetByIdAsync(input.ChatId.Value);
            if (existingChat == null || existingChat.UserId != userId)
                throw Oops.Oh("聊天会话不存在或无权限访问");

            return existingChat;
        }

        // 创建新会话
        var newChat = new SysAiChat
        {
            Title = !string.IsNullOrWhiteSpace(input.Title) ? input.Title :
                    (input.Content.Length > 20 ? input.Content.Substring(0, 20) + "..." : input.Content),
            UserId = userId,
            UserName = userName,
            Status = (int)AiChatStatusEnum.Active,
            LastMessageTime = DateTime.Now,
            MessageCount = 0,
            TenantId = _userManager.TenantId
        };

        return await _sysAiChatRep.InsertReturnEntityAsync(newChat);
    }

    /// <summary>
    /// 保存用户消息
    /// </summary>
    private async Task<SysAiChatMessage> SaveUserMessage(long chatId, string content, long userId, string userName)
    {
        var sequence = await GetNextSequence(chatId);

        var message = new SysAiChatMessage
        {
            ChatId = chatId,
            Role = "user",
            Content = content,
            Sequence = sequence,
            UserId = userId,
            UserName = userName,
            Status = 0,
            TenantId = _userManager.TenantId
        };

        return await _sysAiChatMessageRep.InsertReturnEntityAsync(message);
    }

    /// <summary>
    /// 保存AI消息
    /// </summary>
    private async Task<SysAiChatMessage> SaveAiMessage(long chatId, string content, string modelName,
        int processTime, int tokenUsage, long userId, string userName)
    {
        var sequence = await GetNextSequence(chatId);

        var message = new SysAiChatMessage
        {
            ChatId = chatId,
            Role = "assistant",
            Content = content,
            Sequence = sequence,
            UserId = userId,
            UserName = userName,
            Status = 0,
            ModelName = modelName,
            ProcessTime = processTime,
            TokenUsage = tokenUsage,
            TenantId = _userManager.TenantId
        };

        return await _sysAiChatMessageRep.InsertReturnEntityAsync(message);
    }

    /// <summary>
    /// 获取下一个消息序号
    /// </summary>
    private async Task<int> GetNextSequence(long chatId)
    {
        var maxSequence = await _sysAiChatMessageRep.AsQueryable()
            .Where(u => u.ChatId == chatId)
            .MaxAsync(u => (int?)u.Sequence) ?? 0;

        return maxSequence + 1;
    }

    /// <summary>
    /// 更新会话信息
    /// </summary>
    private async Task UpdateChatInfo(long chatId, int messageCount)
    {
        await _sysAiChatRep.UpdateAsync(
            u => u.Id == chatId,
            u => new SysAiChat
            {
                LastMessageTime = DateTime.Now,
                MessageCount = messageCount
            });
    }

    /// <summary>
    /// 获取聊天历史
    /// </summary>
    private async Task<List<ChatHistoryOutput>> GetChatHistory(long chatId)
    {
        return await _sysAiChatMessageRep.AsQueryable()
            .Where(u => u.ChatId == chatId && u.Status == 0)
            .OrderBy(u => u.Sequence)
            .Select(u => new ChatHistoryOutput
            {
                Id = u.Id,
                Role = u.Role,
                Content = u.Content,
                Sequence = u.Sequence,
                UserName = u.UserName,
                ModelName = u.ModelName,
                ProcessTime = u.ProcessTime,
                TokenUsage = u.TokenUsage,
                CreateTime = u.CreateTime
            })
            .ToListAsync();
    }

    /// <summary>
    /// 搜索相关知识库
    /// </summary>
    private async Task<List<KnowledgeSearchResult>> SearchKnowledge(string query)
    {
        var searchInput = new SearchKnowledgeInput
        {
            Query = query,
            SearchType = "semantic",
            SimilarityThreshold = _aiChatOptions.Value.KnowledgeSimilarityThreshold,
            MaxResults = _aiChatOptions.Value.MaxKnowledgeRetrievalCount,
            OnlyEnabled = true
        };

        return await _aiKnowledgeService.SearchKnowledge(searchInput);
    }

    /// <summary>
    /// 构建AI提示词
    /// </summary>
    private async Task<string> BuildPrompt(string userMessage, List<ChatHistoryOutput> chatHistory,
        List<KnowledgeSearchResult> relatedKnowledge)
    {
        return await _aiChatEngine.BuildPrompt(userMessage, chatHistory, relatedKnowledge);
    }

    /// <summary>
    /// 获取AI回复
    /// </summary>
    private async Task<AiChatEngine.AiResponse> GetAiResponse(string prompt, string modelName)
    {
        return await _aiChatEngine.GetAiResponse(prompt, modelName);
    }

    /// <summary>
    /// 发送实时消息
    /// </summary>
    private async Task SendRealtimeMessage(long chatId, SysAiChatMessage message)
    {
        try
        {
            var messageData = new
            {
                ChatId = chatId,
                MessageId = message.Id,
                Role = message.Role,
                Content = message.Content,
                UserName = message.UserName,
                ModelName = message.ModelName,
                CreateTime = message.CreateTime
            };

            // 发送给特定用户
            var hashKey = _sysCacheService.HashGetAll<SysOnlineUser>(CacheConst.KeyUserOnline);
            var onlineUsers = hashKey.Where(u => u.Value.UserId == message.UserId).Select(u => u.Value).ToList();

            foreach (var user in onlineUsers)
            {
                await _chatHubContext.Clients.Client(user.ConnectionId ?? "").ReceiveMessage(messageData);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送实时消息失败 - 聊天ID:{ChatId}, 消息ID:{MessageId}", chatId, message.Id);
        }
    }

    #endregion
}
