// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// AI知识库分页查询输入参数
/// </summary>
public class PageAiKnowledgeInput : BasePageInput
{
    /// <summary>
    /// 知识标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 知识分类
    /// </summary>
    public string Category { get; set; }

    /// <summary>
    /// 关键词
    /// </summary>
    public string Keywords { get; set; }

    /// <summary>
    /// 知识状态
    /// </summary>
    public AiKnowledgeStatusEnum? Status { get; set; }

    /// <summary>
    /// 知识来源
    /// </summary>
    public string Source { get; set; }
}

/// <summary>
/// 添加AI知识库输入参数
/// </summary>
public class AddAiKnowledgeInput : SysAiKnowledge
{
    /// <summary>
    /// 知识标题
    /// </summary>
    [Required(ErrorMessage = "知识标题不能为空")]
    public new string Title { get; set; }

    /// <summary>
    /// 知识内容
    /// </summary>
    [Required(ErrorMessage = "知识内容不能为空")]
    public new string Content { get; set; }
}

/// <summary>
/// 更新AI知识库输入参数
/// </summary>
public class UpdateAiKnowledgeInput : AddAiKnowledgeInput
{
}

/// <summary>
/// 删除AI知识库输入参数
/// </summary>
public class DeleteAiKnowledgeInput : BaseIdInput
{
}

/// <summary>
/// 批量导入知识库输入参数
/// </summary>
public class ImportKnowledgeInput
{
    /// <summary>
    /// 知识库数据列表
    /// </summary>
    [Required(ErrorMessage = "知识库数据不能为空")]
    public List<KnowledgeItem> KnowledgeList { get; set; }

    /// <summary>
    /// 是否覆盖已存在的知识
    /// </summary>
    public bool OverwriteExisting { get; set; } = false;

    /// <summary>
    /// 默认分类
    /// </summary>
    public string DefaultCategory { get; set; } = "通用";

    /// <summary>
    /// 默认来源
    /// </summary>
    public string DefaultSource { get; set; } = "批量导入";
}

/// <summary>
/// 知识库条目
/// </summary>
public class KnowledgeItem
{
    /// <summary>
    /// 标题
    /// </summary>
    [Required(ErrorMessage = "标题不能为空")]
    public string Title { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    [Required(ErrorMessage = "内容不能为空")]
    public string Content { get; set; }

    /// <summary>
    /// 分类
    /// </summary>
    public string Category { get; set; }

    /// <summary>
    /// 关键词
    /// </summary>
    public string Keywords { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public string Source { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; } = 0;
}

/// <summary>
/// 知识库搜索输入参数
/// </summary>
public class SearchKnowledgeInput
{
    /// <summary>
    /// 搜索关键词
    /// </summary>
    [Required(ErrorMessage = "搜索关键词不能为空")]
    public string Query { get; set; }

    /// <summary>
    /// 搜索类型（keyword：关键词搜索，semantic：语义搜索）
    /// </summary>
    public string SearchType { get; set; } = "semantic";

    /// <summary>
    /// 相似度阈值
    /// </summary>
    public double SimilarityThreshold { get; set; } = 0.7;

    /// <summary>
    /// 最大返回数量
    /// </summary>
    public int MaxResults { get; set; } = 10;

    /// <summary>
    /// 指定分类
    /// </summary>
    public string Category { get; set; }

    /// <summary>
    /// 是否只搜索启用的知识
    /// </summary>
    public bool OnlyEnabled { get; set; } = true;
}

/// <summary>
/// 知识库搜索结果
/// </summary>
public class KnowledgeSearchResult
{
    /// <summary>
    /// 知识ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    /// 分类
    /// </summary>
    public string Category { get; set; }

    /// <summary>
    /// 关键词
    /// </summary>
    public string Keywords { get; set; }

    /// <summary>
    /// 相似度分数
    /// </summary>
    public double Score { get; set; }

    /// <summary>
    /// 使用次数
    /// </summary>
    public int UsageCount { get; set; }
}
