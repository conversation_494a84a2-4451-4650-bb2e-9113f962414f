// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Application.Service.ProductionPlans.Dto;
using Admin.NET.Core.Entity.MesEntity;
using Admin.NET.Core.Enum;
using Admin.NET.Core.EventBus.RabbitMQ;
using Furion.DatabaseAccessor;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System.Text;
using System.Text.Json;

namespace Admin.NET.Application.Service.ProductionPlans;

/// <summary>
/// 生产计划 RabbitMQ 消费者服务
/// </summary>
public class ProductionPlanRabbitMQConsumer : IHostedService, IDisposable
{
    private readonly ILogger<ProductionPlanRabbitMQConsumer> _logger;
    private readonly IServiceProvider _serviceProvider;
    private IConnection? _connection;
    private IChannel? _decomposeChannel;
    private IChannel? _statusChannel;
    private IChannel? _workOrderChannel;
    private readonly CancellationTokenSource _cancellationTokenSource;

    public ProductionPlanRabbitMQConsumer(
        ILogger<ProductionPlanRabbitMQConsumer> logger,
        IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _cancellationTokenSource = new CancellationTokenSource();
    }

    /// <summary>
    /// 启动服务
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("启动生产计划 RabbitMQ 消费者服务");

        try
        {
            // 检查 RabbitMQ 是否可用
            var healthCheckLogger = App.GetRequiredService<ILogger<RabbitMQHealthCheckService>>();
            var healthCheckService = new RabbitMQHealthCheckService(healthCheckLogger);
            var isAvailable = await healthCheckService.WaitForRabbitMQAsync(30, cancellationToken);

            if (!isAvailable)
            {
                _logger.LogWarning("RabbitMQ 不可用，消费者服务将不会启动。请确保 RabbitMQ 服务正在运行。");
                return;
            }

            var factory = ProductionPlanRabbitMQManager.GetConnectionFactory();
            _connection = await factory.CreateConnectionAsync();

            // 创建分解任务消费通道
            _decomposeChannel = await _connection.CreateChannelAsync();
            await ProductionPlanRabbitMQManager.InitializeQueuesAndExchangesAsync(_decomposeChannel);
            await _decomposeChannel.BasicQosAsync(0, 1, false); // 设置预取数量
            await StartDecomposeConsumer();

            // 创建状态更新消费通道
            _statusChannel = await _connection.CreateChannelAsync();
            await _statusChannel.BasicQosAsync(0, 1, false);
            await StartStatusUpdateConsumer();

            // 创建工单生成消费通道
            _workOrderChannel = await _connection.CreateChannelAsync();
            await _workOrderChannel.BasicQosAsync(0, 1, false);
            await StartWorkOrderConsumer();

            _logger.LogInformation("生产计划 RabbitMQ 消费者服务启动成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动生产计划 RabbitMQ 消费者服务失败，将跳过 RabbitMQ 消费者启动");
            // 不抛出异常，允许应用程序继续启动
        }
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("停止生产计划 RabbitMQ 消费者服务");

        _cancellationTokenSource.Cancel();

        if (_decomposeChannel?.IsOpen == true)
            await _decomposeChannel.CloseAsync();

        if (_statusChannel?.IsOpen == true)
            await _statusChannel.CloseAsync();

        if (_workOrderChannel?.IsOpen == true)
            await _workOrderChannel.CloseAsync();

        if (_connection?.IsOpen == true)
            await _connection.CloseAsync();

        _logger.LogInformation("生产计划 RabbitMQ 消费者服务已停止");
    }

    /// <summary>
    /// 启动分解任务消费者
    /// </summary>
    /// <returns></returns>
    private async Task StartDecomposeConsumer()
    {
        var consumer = new AsyncEventingBasicConsumer(_decomposeChannel);
        consumer.ReceivedAsync += async (model, ea) =>
        {
            var body = ea.Body.ToArray();
            var messageText = Encoding.UTF8.GetString(body);

            try
            {
                var message = JsonSerializer.Deserialize<ProductionPlanDecomposeRabbitMQMessage>(messageText);
                if (message != null)
                {
                    await ProcessDecomposeMessage(message);
                    await _decomposeChannel.BasicAckAsync(ea.DeliveryTag, false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理分解消息失败: {Message}", messageText);
                
                // 检查重试次数
                var retryCount = ea.BasicProperties.Headers?.ContainsKey("retry_count") == true 
                    ? Convert.ToInt32(ea.BasicProperties.Headers["retry_count"]) : 0;
                var maxRetryCount = ea.BasicProperties.Headers?.ContainsKey("max_retry_count") == true 
                    ? Convert.ToInt32(ea.BasicProperties.Headers["max_retry_count"]) : 3;

                if (retryCount < maxRetryCount)
                {
                    // 重新发布到重试队列
                    var message = JsonSerializer.Deserialize<ProductionPlanDecomposeRabbitMQMessage>(messageText);
                    if (message != null)
                    {
                        var delaySeconds = (int)Math.Pow(2, retryCount + 1) * 60; // 指数退避
                        await ProductionPlanRabbitMQManager.PublishRetryAsync(message, delaySeconds);
                    }
                }
                else
                {
                    _logger.LogError("消息重试次数已达上限，发送到死信队列: {Message}", messageText);
                }

                await _decomposeChannel.BasicNackAsync(ea.DeliveryTag, false, false);
            }
        };

        await _decomposeChannel.BasicConsumeAsync(
            queue: ProductionPlanRabbitMQManager.DECOMPOSE_QUEUE,
            autoAck: false,
            consumer: consumer);

        _logger.LogInformation("分解任务消费者已启动");
    }

    /// <summary>
    /// 启动状态更新消费者
    /// </summary>
    /// <returns></returns>
    private async Task StartStatusUpdateConsumer()
    {
        var consumer = new AsyncEventingBasicConsumer(_statusChannel);
        consumer.ReceivedAsync += async (model, ea) =>
        {
            var body = ea.Body.ToArray();
            var messageText = Encoding.UTF8.GetString(body);

            try
            {
                var message = JsonSerializer.Deserialize<ProductionPlanStatusRabbitMQMessage>(messageText);
                if (message != null)
                {
                    await ProcessStatusUpdateMessage(message);
                    await _statusChannel.BasicAckAsync(ea.DeliveryTag, false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理状态更新消息失败: {Message}", messageText);
                await _statusChannel.BasicNackAsync(ea.DeliveryTag, false, true); // 重新入队
            }
        };

        await _statusChannel.BasicConsumeAsync(
            queue: ProductionPlanRabbitMQManager.STATUS_UPDATE_QUEUE,
            autoAck: false,
            consumer: consumer);

        _logger.LogInformation("状态更新消费者已启动");
    }

    /// <summary>
    /// 启动工单生成消费者
    /// </summary>
    /// <returns></returns>
    private async Task StartWorkOrderConsumer()
    {
        var consumer = new AsyncEventingBasicConsumer(_workOrderChannel);
        consumer.ReceivedAsync += async (model, ea) =>
        {
            var body = ea.Body.ToArray();
            var messageText = Encoding.UTF8.GetString(body);

            try
            {
                var message = JsonSerializer.Deserialize<WorkOrderGenerationRabbitMQMessage>(messageText);
                if (message != null)
                {
                    await ProcessWorkOrderMessage(message);
                    await _workOrderChannel.BasicAckAsync(ea.DeliveryTag, false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理工单生成消息失败: {Message}", messageText);
                await _workOrderChannel.BasicNackAsync(ea.DeliveryTag, false, true); // 重新入队
            }
        };

        await _workOrderChannel.BasicConsumeAsync(
            queue: ProductionPlanRabbitMQManager.WORK_ORDER_QUEUE,
            autoAck: false,
            consumer: consumer);

        _logger.LogInformation("工单生成消费者已启动");
    }

    /// <summary>
    /// 处理分解任务消息
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    private async Task ProcessDecomposeMessage(ProductionPlanDecomposeRabbitMQMessage message)
    {
        using var scope = _serviceProvider.CreateScope();
        var productionPlanService = scope.ServiceProvider.GetRequiredService<ProductionPlanService>();
        var productionPlanRep = scope.ServiceProvider.GetRequiredService<SqlSugarRepository<ProductionPlan>>();

        try
        {
            _logger.LogInformation("开始处理生产计划分解任务: PlanId={PlanId}, MessageId={MessageId}", 
                message.PlanId, message.MessageId);

            // 检查计划是否存在且状态正确
            var plan = await productionPlanRep.GetFirstAsync(p => p.Id == message.PlanId);
            if (plan == null)
            {
                _logger.LogWarning("生产计划不存在: PlanId={PlanId}", message.PlanId);
                return;
            }

            if (plan.PlanStatus != (int)ProductionPlanStatusEnum.Undecomposed)
            {
                _logger.LogWarning("生产计划状态不正确，无法分解: PlanId={PlanId}, Status={Status}", 
                    message.PlanId, plan.PlanStatus);
                return;
            }

            // 更新计划状态为分解中
            await ProductionPlanRabbitMQManager.PublishStatusUpdateAsync(new ProductionPlanStatusRabbitMQMessage
            {
                PlanId = message.PlanId,
                OldStatus = (ProductionPlanStatusEnum)plan.PlanStatus,
                NewStatus = ProductionPlanStatusEnum.Decomposing,
                Reason = "开始异步分解",
                OperatorUserId = message.CreateUserId,
                OperatorUserName = message.CreateUserName
            });

            // 执行分解
            var input = new DecomposeProductionPlanInput { PlanId = message.PlanId };
            var result = await productionPlanService.DecomposeProductionPlanAsync(input);

            if (result.Success)
            {
                _logger.LogInformation("生产计划分解成功: PlanId={PlanId}, MaterialCount={MaterialCount}", 
                    message.PlanId, result.DecomposedMaterials?.Count ?? 0);

                // 更新状态为已分解
                await ProductionPlanRabbitMQManager.PublishStatusUpdateAsync(new ProductionPlanStatusRabbitMQMessage
                {
                    PlanId = message.PlanId,
                    OldStatus = ProductionPlanStatusEnum.Decomposing,
                    NewStatus = ProductionPlanStatusEnum.Decomposed,
                    Reason = "分解完成",
                    OperatorUserId = message.CreateUserId,
                    OperatorUserName = message.CreateUserName
                });
            }
            else
            {
                _logger.LogError("生产计划分解失败: PlanId={PlanId}, Error={Error}", 
                    message.PlanId, result.Message);

                // 更新状态为分解失败
                await ProductionPlanRabbitMQManager.PublishStatusUpdateAsync(new ProductionPlanStatusRabbitMQMessage
                {
                    PlanId = message.PlanId,
                    OldStatus = ProductionPlanStatusEnum.Decomposing,
                    NewStatus = ProductionPlanStatusEnum.DecomposeFailed,
                    Reason = $"分解失败: {result.Message}",
                    OperatorUserId = message.CreateUserId,
                    OperatorUserName = message.CreateUserName
                });

                throw new Exception(result.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理生产计划分解任务时发生异常: PlanId={PlanId}", message.PlanId);
            throw;
        }
    }

    /// <summary>
    /// 处理状态更新消息
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    private async Task ProcessStatusUpdateMessage(ProductionPlanStatusRabbitMQMessage message)
    {
        using var scope = _serviceProvider.CreateScope();
        var productionPlanRep = scope.ServiceProvider.GetRequiredService<SqlSugarRepository<ProductionPlan>>();

        try
        {
            _logger.LogInformation("开始处理生产计划状态更新: PlanId={PlanId}, Status={OldStatus}->{NewStatus}, Reason={Reason}",
                message.PlanId, message.OldStatus, message.NewStatus, message.Reason);

            // 先查询当前计划状态
            var currentPlan = await productionPlanRep.GetFirstAsync(p => p.Id == message.PlanId);
            if (currentPlan == null)
            {
                _logger.LogWarning("要更新状态的生产计划不存在: PlanId={PlanId}", message.PlanId);
                return;
            }

            _logger.LogInformation("当前计划状态: PlanId={PlanId}, CurrentStatus={CurrentStatus}, TargetStatus={TargetStatus}",
                message.PlanId, currentPlan.PlanStatus, (int)message.NewStatus);

            // 更新数据库中的计划状态
            var updateResult = await productionPlanRep.AsUpdateable()
                .SetColumns(u => u.PlanStatus == (int)message.NewStatus)
                .SetColumns(u => u.UpdateTime == DateTime.Now)
                .Where(u => u.Id == message.PlanId)
                .ExecuteCommandAsync();

            if (updateResult > 0)
            {
                _logger.LogInformation("生产计划状态更新成功: PlanId={PlanId}, 影响行数={UpdateResult}, 新状态={NewStatus}",
                    message.PlanId, updateResult, message.NewStatus);
            }
            else
            {
                _logger.LogWarning("生产计划状态更新失败，没有影响任何行: PlanId={PlanId}", message.PlanId);
            }

            // 验证更新结果
            var updatedPlan = await productionPlanRep.GetFirstAsync(p => p.Id == message.PlanId);
            if (updatedPlan != null)
            {
                _logger.LogInformation("状态更新验证: PlanId={PlanId}, 数据库中的状态={DatabaseStatus}, 期望状态={ExpectedStatus}",
                    message.PlanId, updatedPlan.PlanStatus, (int)message.NewStatus);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理生产计划状态更新时发生异常: PlanId={PlanId}, NewStatus={NewStatus}",
                message.PlanId, message.NewStatus);
            throw;
        }
    }

    /// <summary>
    /// 处理工单生成消息
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    private async Task ProcessWorkOrderMessage(WorkOrderGenerationRabbitMQMessage message)
    {
        try
        {
            _logger.LogInformation("处理工单生成消息: PlanId={PlanId}, WorkOrderType={WorkOrderType}",
                message.PlanId, message.WorkOrderType);

            // 这里可以添加实际的工单生成逻辑
            // 例如调用工单服务创建工单
            await Task.Delay(100); // 模拟异步处理

            _logger.LogInformation("工单生成处理完成: PlanId={PlanId}", message.PlanId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理工单生成消息时发生异常: PlanId={PlanId}", message.PlanId);
            throw;
        }
    }

    public void Dispose()
    {
        _cancellationTokenSource?.Cancel();
        _decomposeChannel?.Dispose();
        _statusChannel?.Dispose();
        _workOrderChannel?.Dispose();
        _connection?.Dispose();
        _cancellationTokenSource?.Dispose();
    }
}
