// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Application.Service.ProductionPlans.Dto;
using Admin.NET.Core.Entity.MesEntity;
using Admin.NET.Core.Enum;
using Admin.NET.Core.EventBus;
using Furion.DatabaseAccessor;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Admin.NET.Application.Service.ProductionPlans;

/// <summary>
/// 生产计划分解消费者
/// </summary>
public class ProductionPlanDecomposeConsumer : IHostedService, IDisposable
{
    private readonly ILogger<ProductionPlanDecomposeConsumer> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly CancellationTokenSource _cancellationTokenSource;
    private Task? _consumeTask;
    private Task? _statusUpdateTask;

    public ProductionPlanDecomposeConsumer(
        ILogger<ProductionPlanDecomposeConsumer> logger,
        IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _cancellationTokenSource = new CancellationTokenSource();
    }

    /// <summary>
    /// 启动服务
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("生产计划分解消费者服务启动");

        // 启动分解任务消费者
        _consumeTask = Task.Run(async () => await ConsumeDecomposeMessages(_cancellationTokenSource.Token), cancellationToken);
        
        // 启动状态更新消费者
        _statusUpdateTask = Task.Run(async () => await ConsumeStatusUpdateMessages(_cancellationTokenSource.Token), cancellationToken);

        return Task.CompletedTask;
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("生产计划分解消费者服务停止");

        _cancellationTokenSource.Cancel();

        if (_consumeTask != null)
            await _consumeTask;
        if (_statusUpdateTask != null)
            await _statusUpdateTask;
    }

    /// <summary>
    /// 消费分解任务消息
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    private async Task ConsumeDecomposeMessages(CancellationToken cancellationToken)
    {
        var queue = ProductionPlanDecomposeQueue.GetDecomposeQueue();
        
        _logger.LogInformation("开始消费生产计划分解任务消息");

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var message = queue.TakeOne(1000); // 1秒超时
                if (message != null)
                {
                    await ProcessDecomposeMessage(message);
                }
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "消费分解任务消息时发生错误");
                await Task.Delay(5000, cancellationToken); // 错误后等待5秒
            }
        }
    }

    /// <summary>
    /// 消费状态更新消息
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    private async Task ConsumeStatusUpdateMessages(CancellationToken cancellationToken)
    {
        var queue = ProductionPlanDecomposeQueue.GetStatusUpdateQueue();
        
        _logger.LogInformation("开始消费生产计划状态更新消息");

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var message = queue.TakeOne(1000); // 1秒超时
                if (message != null)
                {
                    await ProcessStatusUpdateMessage(message);
                }
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "消费状态更新消息时发生错误");
                await Task.Delay(5000, cancellationToken);
            }
        }
    }

    /// <summary>
    /// 处理分解任务消息
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    private async Task ProcessDecomposeMessage(ProductionPlanDecomposeMessage message)
    {
        using var scope = _serviceProvider.CreateScope();
        var productionPlanService = scope.ServiceProvider.GetRequiredService<ProductionPlanService>();
        var productionPlanRep = scope.ServiceProvider.GetRequiredService<SqlSugarRepository<ProductionPlan>>();

        try
        {
            _logger.LogInformation("开始处理生产计划分解任务: PlanId={PlanId}, MessageId={MessageId}", 
                message.PlanId, message.MessageId);

            // 检查计划是否存在且状态正确
            var plan = await productionPlanRep.GetFirstAsync(p => p.Id == message.PlanId);
            if (plan == null)
            {
                _logger.LogWarning("生产计划不存在: PlanId={PlanId}", message.PlanId);
                return;
            }

            if (plan.PlanStatus != (int)ProductionPlanStatusEnum.Undecomposed)
            {
                _logger.LogWarning("生产计划状态不正确，无法分解: PlanId={PlanId}, Status={Status}", 
                    message.PlanId, plan.PlanStatus);
                return;
            }

            // 更新计划状态为分解中
            await ProductionPlanDecomposeQueue.PublishStatusUpdateAsync(new ProductionPlanStatusMessage
            {
                PlanId = message.PlanId,
                OldStatus = (ProductionPlanStatusEnum)plan.PlanStatus,
                NewStatus = ProductionPlanStatusEnum.Decomposing,
                Reason = "开始异步分解",
                OperatorUserId = message.CreateUserId,
                OperatorUserName = message.CreateUserName
            });

            // 执行分解
            var input = new DecomposeProductionPlanInput { PlanId = message.PlanId };
            var result = await productionPlanService.DecomposeProductionPlanAsync(input);

            if (result.Success)
            {
                _logger.LogInformation("生产计划分解成功: PlanId={PlanId}, MaterialCount={MaterialCount}", 
                    message.PlanId, result.DecomposedMaterials?.Count ?? 0);

                // 更新状态为已分解
                await ProductionPlanDecomposeQueue.PublishStatusUpdateAsync(new ProductionPlanStatusMessage
                {
                    PlanId = message.PlanId,
                    OldStatus = ProductionPlanStatusEnum.Decomposing,
                    NewStatus = ProductionPlanStatusEnum.Decomposed,
                    Reason = "分解完成",
                    OperatorUserId = message.CreateUserId,
                    OperatorUserName = message.CreateUserName
                });
            }
            else
            {
                _logger.LogError("生产计划分解失败: PlanId={PlanId}, Error={Error}", 
                    message.PlanId, result.Message);

                // 处理重试逻辑
                await HandleRetry(message, result.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理生产计划分解任务时发生异常: PlanId={PlanId}", message.PlanId);
            
            // 处理重试逻辑
            await HandleRetry(message, ex.Message);
        }
    }

    /// <summary>
    /// 处理状态更新消息
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    private async Task ProcessStatusUpdateMessage(ProductionPlanStatusMessage message)
    {
        using var scope = _serviceProvider.CreateScope();
        var productionPlanRep = scope.ServiceProvider.GetRequiredService<SqlSugarRepository<ProductionPlan>>();

        try
        {
            _logger.LogInformation("处理生产计划状态更新: PlanId={PlanId}, Status={OldStatus}->{NewStatus}", 
                message.PlanId, message.OldStatus, message.NewStatus);

            // 更新数据库中的计划状态
            await productionPlanRep.AsUpdateable()
                .SetColumns(u => u.PlanStatus == (int)message.NewStatus)
                .Where(u => u.Id == message.PlanId)
                .ExecuteCommandAsync();

            _logger.LogInformation("生产计划状态更新成功: PlanId={PlanId}", message.PlanId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理生产计划状态更新时发生异常: PlanId={PlanId}", message.PlanId);
        }
    }

    /// <summary>
    /// 处理重试逻辑
    /// </summary>
    /// <param name="message"></param>
    /// <param name="errorMessage"></param>
    /// <returns></returns>
    private async Task HandleRetry(ProductionPlanDecomposeMessage message, string errorMessage)
    {
        message.RetryCount++;

        if (message.RetryCount <= message.MaxRetryCount)
        {
            // 计算延迟时间（指数退避）
            int delaySeconds = (int)Math.Pow(2, message.RetryCount) * 60; // 2^n 分钟
            
            _logger.LogInformation("生产计划分解任务将在{DelaySeconds}秒后重试: PlanId={PlanId}, RetryCount={RetryCount}", 
                delaySeconds, message.PlanId, message.RetryCount);

            await ProductionPlanDecomposeQueue.PublishDelayRetryAsync(message, delaySeconds);
        }
        else
        {
            _logger.LogError("生产计划分解任务重试次数已达上限，标记为失败: PlanId={PlanId}, Error={Error}", 
                message.PlanId, errorMessage);

            // 更新状态为分解失败
            await ProductionPlanDecomposeQueue.PublishStatusUpdateAsync(new ProductionPlanStatusMessage
            {
                PlanId = message.PlanId,
                OldStatus = ProductionPlanStatusEnum.Decomposing,
                NewStatus = ProductionPlanStatusEnum.DecomposeFailed,
                Reason = $"分解失败: {errorMessage}",
                OperatorUserId = message.CreateUserId,
                OperatorUserName = message.CreateUserName
            });
        }
    }

    public void Dispose()
    {
        _cancellationTokenSource?.Cancel();
        _cancellationTokenSource?.Dispose();
    }
}
