# Admin.NET MES 智能客服功能集成指南

## 概述

本项目已成功集成LangChain开发的智能客服功能，为MES系统提供了专业的AI助手服务。智能客服具备自然语言处理、知识库问答、多轮对话等核心功能。

## 功能特性

### 🤖 核心功能
- **智能对话**: 基于GPT模型的自然语言对话
- **知识库检索**: 智能检索MES相关知识库内容
- **多轮对话**: 支持上下文记忆的连续对话
- **实时通信**: 基于SignalR的实时消息推送
- **内容过滤**: 敏感词过滤和内容安全检查

### 📊 管理功能
- **会话管理**: 创建、查看、删除聊天会话
- **消息历史**: 完整的对话历史记录
- **知识库管理**: 添加、编辑、删除知识库条目
- **配置管理**: 灵活的AI参数配置
- **使用统计**: 详细的使用数据分析

## 技术架构

### 🏗️ 核心组件
- **LangChain.NET**: AI模型集成框架
- **OpenAI API**: GPT模型服务
- **SqlSugar**: 数据持久化
- **SignalR**: 实时通信
- **Furion**: 应用框架

### 📁 项目结构
```
Admin.NET.Core/
├── Entity/                     # 实体类
│   ├── SysAiChat.cs           # 聊天会话实体
│   ├── SysAiChatMessage.cs    # 聊天消息实体
│   ├── SysAiKnowledge.cs      # 知识库实体
│   └── SysAiConfig.cs         # AI配置实体
├── Enum/                       # 枚举类型
│   ├── AiChatRoleEnum.cs      # 聊天角色枚举
│   ├── AiChatStatusEnum.cs    # 聊天状态枚举
│   └── AiKnowledgeStatusEnum.cs # 知识库状态枚举
├── Service/AiChat/            # 服务层
│   ├── SysAiChatService.cs    # 聊天服务
│   ├── SysAiKnowledgeService.cs # 知识库服务
│   ├── AiChatEngine.cs        # AI引擎
│   └── Dto/                   # 数据传输对象
├── Option/                     # 配置选项
│   └── AiChatOptions.cs       # AI聊天配置
├── SeedData/                   # 种子数据
│   ├── SysAiKnowledgeSeedData.cs # 知识库初始数据
│   └── SysAiConfigSeedData.cs    # 配置初始数据
└── Startup/                    # 启动配置
    └── AiChatStartup.cs       # AI聊天服务注册
```

## 配置说明

### 🔧 基础配置

在 `Admin.NET.Web.Entry/Configuration/AiChat.json` 中配置AI参数：

```json
{
  "AiChat": {
    "OpenAiApiKey": "your-openai-api-key",
    "OpenAiBaseUrl": "https://api.openai.com/v1",
    "DefaultModel": "gpt-3.5-turbo",
    "MaxTokens": 2000,
    "Temperature": 0.7,
    "SystemPrompt": "你是一个专业的MES系统智能助手...",
    "MaxHistoryLength": 20,
    "EnableKnowledgeRetrieval": true,
    "KnowledgeSimilarityThreshold": 0.7,
    "MaxKnowledgeRetrievalCount": 5,
    "EnableContentFilter": true,
    "SensitiveWords": ["政治", "暴力", "色情"],
    "EnableLogging": true,
    "EnableCache": true,
    "CacheExpirationMinutes": 60
  }
}
```

### 🔑 必需配置项
- `OpenAiApiKey`: OpenAI API密钥（必填）
- `DefaultModel`: 默认AI模型名称
- `SystemPrompt`: 系统提示词，定义AI助手的角色

### ⚙️ 可选配置项
- `MaxTokens`: 最大Token数量
- `Temperature`: 回复随机性控制（0-2）
- `EnableKnowledgeRetrieval`: 是否启用知识库检索
- `EnableContentFilter`: 是否启用内容过滤
- `EnableCache`: 是否启用响应缓存

## API接口

### 💬 聊天相关接口

#### 发送消息
```http
POST /api/sysAiChat/sendMessage
Content-Type: application/json

{
  "chatId": 123,  // 可选，会话ID
  "content": "什么是MES系统？",
  "title": "新会话",  // 创建新会话时的标题
  "useKnowledge": true,  // 是否使用知识库
  "modelName": "gpt-3.5-turbo"  // 可选，指定模型
}
```

#### 获取聊天列表
```http
GET /api/sysAiChat/getUserChatList
```

#### 获取聊天历史
```http
POST /api/sysAiChat/getChatHistory
Content-Type: application/json

{
  "chatId": 123,
  "page": 1,
  "pageSize": 20,
  "includeSystemMessages": false
}
```

### 📚 知识库相关接口

#### 搜索知识库
```http
POST /api/sysAiKnowledge/searchKnowledge
Content-Type: application/json

{
  "query": "生产计划",
  "searchType": "semantic",  // keyword 或 semantic
  "maxResults": 10,
  "category": "生产管理"  // 可选，指定分类
}
```

#### 批量导入知识库
```http
POST /api/sysAiKnowledge/importKnowledge
Content-Type: application/json

{
  "knowledgeList": [
    {
      "title": "知识标题",
      "content": "知识内容",
      "category": "分类",
      "keywords": "关键词",
      "priority": 1
    }
  ],
  "overwriteExisting": false,
  "defaultCategory": "通用",
  "defaultSource": "批量导入"
}
```

## 数据库表结构

### 📋 主要数据表

#### sys_ai_chat (聊天会话表)
- `Id`: 主键
- `Title`: 会话标题
- `UserId`: 用户ID
- `Status`: 会话状态
- `MessageCount`: 消息数量
- `LastMessageTime`: 最后消息时间

#### sys_ai_chat_message (聊天消息表)
- `Id`: 主键
- `ChatId`: 会话ID
- `Role`: 消息角色 (user/assistant/system)
- `Content`: 消息内容
- `Sequence`: 消息序号
- `ModelName`: AI模型名称
- `ProcessTime`: 处理时间
- `TokenUsage`: Token消耗

#### sys_ai_knowledge (知识库表)
- `Id`: 主键
- `Title`: 知识标题
- `Content`: 知识内容
- `Category`: 知识分类
- `Keywords`: 关键词
- `Embedding`: 向量嵌入
- `UsageCount`: 使用次数
- `Priority`: 优先级

## 使用示例

### 🚀 快速开始

1. **配置API密钥**
   ```json
   {
     "AiChat": {
       "OpenAiApiKey": "sk-your-openai-api-key"
     }
   }
   ```

2. **启动应用**
   ```bash
   dotnet run --project Admin.NET.Web.Entry
   ```

3. **测试API**
   ```bash
   curl -X POST "http://localhost:5005/api/sysAiChat/sendMessage" \
        -H "Content-Type: application/json" \
        -d '{"content": "什么是MES系统？"}'
   ```

### 📝 代码示例

#### 在控制器中使用AI聊天服务
```csharp
[ApiController]
[Route("api/[controller]")]
public class ChatController : ControllerBase
{
    private readonly SysAiChatService _aiChatService;

    public ChatController(SysAiChatService aiChatService)
    {
        _aiChatService = aiChatService;
    }

    [HttpPost("ask")]
    public async Task<AiReplyOutput> Ask([FromBody] SendMessageInput input)
    {
        return await _aiChatService.SendMessage(input);
    }
}
```

#### 在前端使用SignalR实时通信
```javascript
const connection = new signalR.HubConnectionBuilder()
    .withUrl("/hubs/onlineUser")
    .build();

// 监听AI回复
connection.on("ReceiveAiChatMessage", function (message) {
    console.log("收到AI回复:", message);
    // 更新UI显示消息
});

// 发送消息
async function sendMessage(content) {
    const response = await fetch('/api/sysAiChat/sendMessage', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content: content })
    });
    return await response.json();
}
```

## 测试

### 🧪 单元测试

运行AI聊天功能的单元测试：

```bash
dotnet test Admin.NET.Tests --filter "AiChatServiceTest"
```

测试覆盖的功能：
- 创建聊天会话
- 发送消息和接收回复
- 知识库搜索
- 内容过滤
- 配置验证

## 故障排除

### ❗ 常见问题

1. **API密钥错误**
   - 检查 `OpenAiApiKey` 配置是否正确
   - 确认API密钥有效且有足够余额

2. **知识库搜索无结果**
   - 检查知识库数据是否已导入
   - 调整相似度阈值参数

3. **消息发送失败**
   - 检查网络连接
   - 查看日志文件获取详细错误信息

4. **实时消息不推送**
   - 确认SignalR连接正常
   - 检查用户权限设置

### 📋 日志查看

查看AI聊天相关日志：
```bash
# 查看今日日志
tail -f Admin.NET.Web.Entry/logs/$(date +%Y%m%d)_Information.log | grep "AI聊天"
```

## 扩展开发

### 🔧 自定义AI模型

1. 实现自定义AI提供者
2. 在 `AiChatEngine` 中添加新的模型支持
3. 更新配置选项

### 📚 扩展知识库

1. 支持文档上传和解析
2. 实现向量数据库集成
3. 添加知识库自动更新功能

## 许可证

本项目遵循 MIT 许可证和 Apache 许可证（版本 2.0）。

## 支持

如有问题或建议，请提交Issue或联系技术支持。
