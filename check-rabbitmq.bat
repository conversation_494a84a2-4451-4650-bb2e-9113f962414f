@echo off
echo 正在检查 RabbitMQ 状态...
echo.

REM 检查端口 5672 是否开放
echo 1. 检查 RabbitMQ 端口 5672...
netstat -an | findstr :5672
if %errorlevel% equ 0 (
    echo ✅ 端口 5672 已开放
) else (
    echo ❌ 端口 5672 未开放
)
echo.

REM 检查端口 15672 是否开放
echo 2. 检查 RabbitMQ 管理端口 15672...
netstat -an | findstr :15672
if %errorlevel% equ 0 (
    echo ✅ 管理端口 15672 已开放
) else (
    echo ❌ 管理端口 15672 未开放
)
echo.

REM 检查 Docker 容器状态
echo 3. 检查 Docker 容器状态...
docker ps | findstr rabbitmq
if %errorlevel% equ 0 (
    echo ✅ RabbitMQ 容器正在运行
) else (
    echo ❌ RabbitMQ 容器未运行
    echo.
    echo 尝试启动 RabbitMQ 容器...
    docker run -d --name rabbitmq -p 5672:5672 -p 15672:15672 -e RABBITMQ_DEFAULT_USER=guest -e RABBITMQ_DEFAULT_PASS=guest rabbitmq:3-management
    if %errorlevel% equ 0 (
        echo ✅ RabbitMQ 容器启动成功
    ) else (
        echo ❌ RabbitMQ 容器启动失败
    )
)
echo.

REM 检查 Docker 是否安装
echo 4. 检查 Docker 状态...
docker --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Docker 已安装
    docker ps >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Docker 服务正在运行
    ) else (
        echo ❌ Docker 服务未运行
        echo 请启动 Docker Desktop
    )
) else (
    echo ❌ Docker 未安装
    echo 请安装 Docker Desktop: https://www.docker.com/products/docker-desktop
)
echo.

echo 5. 解决建议:
echo    - 如果 Docker 未安装，请先安装 Docker Desktop
echo    - 如果 Docker 已安装但未运行，请启动 Docker Desktop
echo    - 如果容器未运行，请运行 start-rabbitmq.bat
echo    - 访问 http://localhost:15672 检查管理界面 (guest/guest)
echo.

pause
