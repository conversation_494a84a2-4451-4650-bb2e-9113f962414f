// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core;

/// <summary>
/// AI聊天消息实体
/// </summary>
[SugarTable(null, "AI聊天消息")]
[SysTable]
public class SysAiChatMessage : EntityBase
{
    /// <summary>
    /// 会话ID
    /// </summary>
    [SugarColumn(ColumnDescription = "会话ID")]
    public long ChatId { get; set; }

    /// <summary>
    /// 消息类型（user：用户消息，assistant：AI回复，system：系统消息）
    /// </summary>
    [SugarColumn(ColumnDescription = "消息类型", Length = 20)]
    [Required, MaxLength(20)]
    public string Role { get; set; }

    /// <summary>
    /// 消息内容
    /// </summary>
    [SugarColumn(ColumnDescription = "消息内容", ColumnDataType = "text")]
    [Required]
    public string Content { get; set; }

    /// <summary>
    /// 消息序号
    /// </summary>
    [SugarColumn(ColumnDescription = "消息序号")]
    public int Sequence { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    [SugarColumn(ColumnDescription = "用户ID")]
    public long UserId { get; set; }

    /// <summary>
    /// 用户名称
    /// </summary>
    [SugarColumn(ColumnDescription = "用户名称", Length = 50)]
    [MaxLength(50)]
    public string UserName { get; set; }

    /// <summary>
    /// 消息状态（0：正常，1：已删除）
    /// </summary>
    [SugarColumn(ColumnDescription = "消息状态")]
    public int Status { get; set; } = 0;

    /// <summary>
    /// AI模型名称
    /// </summary>
    [SugarColumn(ColumnDescription = "AI模型名称", Length = 100)]
    [MaxLength(100)]
    public string ModelName { get; set; }

    /// <summary>
    /// 处理时间（毫秒）
    /// </summary>
    [SugarColumn(ColumnDescription = "处理时间")]
    public int? ProcessTime { get; set; }

    /// <summary>
    /// Token消耗
    /// </summary>
    [SugarColumn(ColumnDescription = "Token消耗")]
    public int? TokenUsage { get; set; }

    /// <summary>
    /// 租户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "租户Id")]
    public virtual long TenantId { get; set; }
}
