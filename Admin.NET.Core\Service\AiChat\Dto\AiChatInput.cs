// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// AI聊天分页查询输入参数
/// </summary>
public class PageAiChatInput : BasePageInput
{
    /// <summary>
    /// 会话标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public long? UserId { get; set; }

    /// <summary>
    /// 会话状态
    /// </summary>
    public AiChatStatusEnum? Status { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

/// <summary>
/// 添加AI聊天输入参数
/// </summary>
public class AddAiChatInput : SysAiChat
{
    /// <summary>
    /// 会话标题
    /// </summary>
    [Required(ErrorMessage = "会话标题不能为空")]
    public new string Title { get; set; }
}

/// <summary>
/// 更新AI聊天输入参数
/// </summary>
public class UpdateAiChatInput : AddAiChatInput
{
}

/// <summary>
/// 删除AI聊天输入参数
/// </summary>
public class DeleteAiChatInput : BaseIdInput
{
}

/// <summary>
/// 发送消息输入参数
/// </summary>
public class SendMessageInput
{
    /// <summary>
    /// 会话ID（可选，如果为空则创建新会话）
    /// </summary>
    public long? ChatId { get; set; }

    /// <summary>
    /// 消息内容
    /// </summary>
    [Required(ErrorMessage = "消息内容不能为空")]
    public string Content { get; set; }

    /// <summary>
    /// 会话标题（创建新会话时使用）
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 是否使用知识库
    /// </summary>
    public bool UseKnowledge { get; set; } = true;

    /// <summary>
    /// 指定模型名称（可选）
    /// </summary>
    public string ModelName { get; set; }
}

/// <summary>
/// AI回复输出参数
/// </summary>
public class AiReplyOutput
{
    /// <summary>
    /// 会话ID
    /// </summary>
    public long ChatId { get; set; }

    /// <summary>
    /// 消息ID
    /// </summary>
    public long MessageId { get; set; }

    /// <summary>
    /// 回复内容
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    /// 使用的模型
    /// </summary>
    public string ModelName { get; set; }

    /// <summary>
    /// 处理时间（毫秒）
    /// </summary>
    public int ProcessTime { get; set; }

    /// <summary>
    /// Token消耗
    /// </summary>
    public int TokenUsage { get; set; }

    /// <summary>
    /// 是否使用了知识库
    /// </summary>
    public bool UsedKnowledge { get; set; }

    /// <summary>
    /// 相关知识库条目
    /// </summary>
    public List<string> RelatedKnowledge { get; set; } = new();
}
