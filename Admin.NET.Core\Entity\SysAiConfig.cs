// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core;

/// <summary>
/// AI配置实体
/// </summary>
[SugarTable(null, "AI配置")]
[SysTable]
public class SysAiConfig : EntityBase
{
    /// <summary>
    /// 配置名称
    /// </summary>
    [SugarColumn(ColumnDescription = "配置名称", Length = 100)]
    [Required, MaxLength(100)]
    public string Name { get; set; }

    /// <summary>
    /// 配置键
    /// </summary>
    [SugarColumn(ColumnDescription = "配置键", Length = 100)]
    [Required, MaxLength(100)]
    public string ConfigKey { get; set; }

    /// <summary>
    /// 配置值
    /// </summary>
    [SugarColumn(ColumnDescription = "配置值", ColumnDataType = "text")]
    public string ConfigValue { get; set; }

    /// <summary>
    /// 配置类型（string、int、bool、json等）
    /// </summary>
    [SugarColumn(ColumnDescription = "配置类型", Length = 50)]
    [MaxLength(50)]
    public string ConfigType { get; set; } = "string";

    /// <summary>
    /// 配置分组
    /// </summary>
    [SugarColumn(ColumnDescription = "配置分组", Length = 100)]
    [MaxLength(100)]
    public string GroupName { get; set; }

    /// <summary>
    /// 配置描述
    /// </summary>
    [SugarColumn(ColumnDescription = "配置描述", Length = 500)]
    [MaxLength(500)]
    public string Description { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启用")]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 是否系统配置（系统配置不允许删除）
    /// </summary>
    [SugarColumn(ColumnDescription = "是否系统配置")]
    public bool IsSystem { get; set; } = false;

    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnDescription = "排序")]
    public int Sort { get; set; } = 100;

    /// <summary>
    /// 租户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "租户Id")]
    public virtual long TenantId { get; set; }
}
