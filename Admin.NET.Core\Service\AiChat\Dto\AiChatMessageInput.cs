// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// AI聊天消息分页查询输入参数
/// </summary>
public class PageAiChatMessageInput : BasePageInput
{
    /// <summary>
    /// 会话ID
    /// </summary>
    public long? ChatId { get; set; }

    /// <summary>
    /// 消息类型
    /// </summary>
    public AiChatRoleEnum? Role { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public long? UserId { get; set; }

    /// <summary>
    /// 消息状态
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

/// <summary>
/// 添加AI聊天消息输入参数
/// </summary>
public class AddAiChatMessageInput : SysAiChatMessage
{
    /// <summary>
    /// 会话ID
    /// </summary>
    [Required(ErrorMessage = "会话ID不能为空")]
    public new long ChatId { get; set; }

    /// <summary>
    /// 消息类型
    /// </summary>
    [Required(ErrorMessage = "消息类型不能为空")]
    public new string Role { get; set; }

    /// <summary>
    /// 消息内容
    /// </summary>
    [Required(ErrorMessage = "消息内容不能为空")]
    public new string Content { get; set; }
}

/// <summary>
/// 更新AI聊天消息输入参数
/// </summary>
public class UpdateAiChatMessageInput : AddAiChatMessageInput
{
}

/// <summary>
/// 删除AI聊天消息输入参数
/// </summary>
public class DeleteAiChatMessageInput : BaseIdInput
{
}

/// <summary>
/// 获取聊天历史输入参数
/// </summary>
public class GetChatHistoryInput
{
    /// <summary>
    /// 会话ID
    /// </summary>
    [Required(ErrorMessage = "会话ID不能为空")]
    public long ChatId { get; set; }

    /// <summary>
    /// 页码
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// 是否包含系统消息
    /// </summary>
    public bool IncludeSystemMessages { get; set; } = false;
}

/// <summary>
/// 聊天历史输出参数
/// </summary>
public class ChatHistoryOutput
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 消息类型
    /// </summary>
    public string Role { get; set; }

    /// <summary>
    /// 消息内容
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    /// 消息序号
    /// </summary>
    public int Sequence { get; set; }

    /// <summary>
    /// 用户名称
    /// </summary>
    public string UserName { get; set; }

    /// <summary>
    /// AI模型名称
    /// </summary>
    public string ModelName { get; set; }

    /// <summary>
    /// 处理时间
    /// </summary>
    public int? ProcessTime { get; set; }

    /// <summary>
    /// Token消耗
    /// </summary>
    public int? TokenUsage { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }
}
