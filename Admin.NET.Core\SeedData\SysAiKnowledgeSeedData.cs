// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core;

/// <summary>
/// AI知识库种子数据
/// </summary>
public class SysAiKnowledgeSeedData : ISqlSugarEntitySeedData<SysAiKnowledge>
{
    /// <summary>
    /// 种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysAiKnowledge> HasData()
    {
        return new[]
        {
            new SysAiKnowledge
            {
                Id = 1400000000001,
                Title = "什么是MES系统",
                Content = "MES（Manufacturing Execution System）制造执行系统是位于上层计划管理系统与底层工业控制之间的、面向车间层的管理信息系统。它为操作人员/管理人员提供计划的执行、跟踪以及所有资源（人、设备、物料、客户需求等）的当前状态。MES系统通过控制包括物料、设备、人员、流程指令和设施在内的所有工厂资源来提高制造竞争力，提供了一种系统地在统一平台上集成诸如质量控制、文档管理、生产调度等功能的方式。",
                Category = "系统介绍",
                Keywords = "MES,制造执行系统,车间管理,生产管理",
                Source = "系统文档",
                Status = (int)AiKnowledgeStatusEnum.Enabled,
                Priority = 10,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            },
            new SysAiKnowledge
            {
                Id = 1400000000002,
                Title = "生产计划管理",
                Content = "生产计划管理是MES系统的核心功能之一，主要包括：1. 主生产计划制定：根据销售订单和库存情况制定生产计划；2. 车间作业计划：将主生产计划分解为具体的车间作业任务；3. 资源调度：合理分配人员、设备、物料等生产资源；4. 进度跟踪：实时监控生产进度，及时发现和解决问题；5. 计划调整：根据实际情况动态调整生产计划。通过生产计划管理，可以提高生产效率，降低库存成本，确保按时交货。",
                Category = "生产管理",
                Keywords = "生产计划,作业计划,资源调度,进度跟踪",
                Source = "系统文档",
                Status = (int)AiKnowledgeStatusEnum.Enabled,
                Priority = 9,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            },
            new SysAiKnowledge
            {
                Id = 1400000000003,
                Title = "工单管理流程",
                Content = "工单管理是MES系统中管理生产任务的重要功能，主要流程包括：1. 工单创建：根据生产计划自动或手动创建工单；2. 工单下发：将工单分配给相应的生产线或工作中心；3. 工单执行：操作人员按照工单要求进行生产作业；4. 进度汇报：实时汇报工单执行进度和完成情况；5. 质量检验：对生产结果进行质量检查；6. 工单完工：确认工单完成并进行结算。工单管理确保生产过程的可追溯性和标准化。",
                Category = "生产管理",
                Keywords = "工单,生产任务,工单流程,生产作业",
                Source = "系统文档",
                Status = (int)AiKnowledgeStatusEnum.Enabled,
                Priority = 9,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            },
            new SysAiKnowledge
            {
                Id = 1400000000004,
                Title = "设备管理与监控",
                Content = "设备管理是MES系统的重要组成部分，主要功能包括：1. 设备档案管理：维护设备的基本信息、技术参数、维护记录等；2. 设备状态监控：实时监控设备运行状态、效率、故障等信息；3. 预防性维护：制定设备维护计划，预防设备故障；4. 设备效率分析：统计分析设备利用率、故障率等关键指标；5. 备件管理：管理设备备件的库存和使用情况。通过设备管理，可以提高设备利用率，减少停机时间，降低维护成本。",
                Category = "设备管理",
                Keywords = "设备管理,设备监控,预防性维护,设备效率",
                Source = "系统文档",
                Status = (int)AiKnowledgeStatusEnum.Enabled,
                Priority = 8,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            },
            new SysAiKnowledge
            {
                Id = 1400000000005,
                Title = "质量管理体系",
                Content = "质量管理是MES系统确保产品质量的关键功能，主要包括：1. 质量标准管理：定义产品质量标准和检验规范；2. 质量检验：在生产过程中进行首检、巡检、终检等质量控制；3. 不合格品处理：对不合格品进行标识、隔离、处置；4. 质量追溯：建立完整的质量追溯体系，可追溯到原材料；5. 质量分析：统计分析质量数据，持续改进质量水平；6. 质量报告：生成各类质量报告和统计图表。质量管理确保产品符合客户要求和行业标准。",
                Category = "质量管理",
                Keywords = "质量管理,质量检验,质量追溯,质量分析",
                Source = "系统文档",
                Status = (int)AiKnowledgeStatusEnum.Enabled,
                Priority = 8,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            },
            new SysAiKnowledge
            {
                Id = 1400000000006,
                Title = "库存管理功能",
                Content = "库存管理是MES系统中管理物料和成品的重要功能，主要包括：1. 库存信息管理：实时掌握原材料、半成品、成品的库存情况；2. 入库管理：处理采购入库、生产入库等业务；3. 出库管理：处理生产领料、销售出库等业务；4. 库存盘点：定期进行库存盘点，确保账实相符；5. 安全库存：设置安全库存水平，及时预警库存不足；6. 库存分析：分析库存周转率、呆滞物料等指标。库存管理帮助企业优化库存结构，降低库存成本。",
                Category = "库存管理",
                Keywords = "库存管理,入库,出库,库存盘点,安全库存",
                Source = "系统文档",
                Status = (int)AiKnowledgeStatusEnum.Enabled,
                Priority = 7,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            },
            new SysAiKnowledge
            {
                Id = 1400000000007,
                Title = "数据采集与分析",
                Content = "数据采集与分析是MES系统的重要功能，为管理决策提供数据支持，主要包括：1. 实时数据采集：通过传感器、PLC等设备实时采集生产数据；2. 数据存储：将采集的数据存储到数据库中，建立历史数据库；3. 数据处理：对原始数据进行清洗、转换、计算等处理；4. 统计分析：生成各类统计报表和分析图表；5. 趋势分析：分析生产趋势，预测未来发展；6. 异常监控：监控异常数据，及时报警。数据采集与分析为企业提供科学的决策依据。",
                Category = "数据分析",
                Keywords = "数据采集,数据分析,统计报表,趋势分析",
                Source = "系统文档",
                Status = (int)AiKnowledgeStatusEnum.Enabled,
                Priority = 7,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            },
            new SysAiKnowledge
            {
                Id = 1400000000008,
                Title = "系统常见问题解答",
                Content = "MES系统使用过程中的常见问题及解决方案：1. 登录问题：检查用户名密码是否正确，确认账号是否被锁定；2. 数据同步问题：检查网络连接，确认数据接口是否正常；3. 报表显示问题：检查数据权限，确认报表参数设置；4. 工单无法下发：检查工单状态，确认生产线是否可用；5. 设备状态异常：检查设备连接，确认数据采集是否正常；6. 系统运行缓慢：检查服务器性能，优化数据库查询。如遇到其他问题，请联系系统管理员或技术支持。",
                Category = "常见问题",
                Keywords = "常见问题,故障排除,技术支持,系统维护",
                Source = "系统文档",
                Status = (int)AiKnowledgeStatusEnum.Enabled,
                Priority = 6,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            }
        };
    }
}
