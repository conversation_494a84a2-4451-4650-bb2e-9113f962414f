// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core;

/// <summary>
/// AI知识库实体
/// </summary>
[SugarTable(null, "AI知识库")]
[SysTable]
public class SysAiKnowledge : EntityBase
{
    /// <summary>
    /// 知识标题
    /// </summary>
    [SugarColumn(ColumnDescription = "知识标题", Length = 200)]
    [Required, MaxLength(200)]
    public string Title { get; set; }

    /// <summary>
    /// 知识内容
    /// </summary>
    [SugarColumn(ColumnDescription = "知识内容", ColumnDataType = "text")]
    [Required]
    public string Content { get; set; }

    /// <summary>
    /// 知识分类
    /// </summary>
    [SugarColumn(ColumnDescription = "知识分类", Length = 100)]
    [MaxLength(100)]
    public string Category { get; set; }

    /// <summary>
    /// 关键词（用于搜索）
    /// </summary>
    [SugarColumn(ColumnDescription = "关键词", Length = 500)]
    [MaxLength(500)]
    public string Keywords { get; set; }

    /// <summary>
    /// 向量嵌入（JSON格式存储）
    /// </summary>
    [SugarColumn(ColumnDescription = "向量嵌入", ColumnDataType = "text")]
    public string Embedding { get; set; }

    /// <summary>
    /// 知识来源
    /// </summary>
    [SugarColumn(ColumnDescription = "知识来源", Length = 200)]
    [MaxLength(200)]
    public string Source { get; set; }

    /// <summary>
    /// 知识状态（0：启用，1：禁用）
    /// </summary>
    [SugarColumn(ColumnDescription = "知识状态")]
    public int Status { get; set; } = 0;

    /// <summary>
    /// 使用次数
    /// </summary>
    [SugarColumn(ColumnDescription = "使用次数")]
    public int UsageCount { get; set; } = 0;

    /// <summary>
    /// 最后使用时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后使用时间")]
    public DateTime? LastUsedTime { get; set; }

    /// <summary>
    /// 优先级（数值越大优先级越高）
    /// </summary>
    [SugarColumn(ColumnDescription = "优先级")]
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 租户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "租户Id")]
    public virtual long TenantId { get; set; }
}
