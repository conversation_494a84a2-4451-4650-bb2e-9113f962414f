// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Application.Service.ProductionPlans;
using Admin.NET.Application.Service.ProductionPlans.Dto;
using Admin.NET.Core.EventBus;
using Microsoft.Extensions.DependencyInjection;
using Xunit;
using Xunit.Abstractions;

namespace Admin.NET.Test.ProductionPlan;

/// <summary>
/// 生产计划消息队列测试
/// </summary>
public class ProductionPlanQueueTest : BaseTest
{
    private readonly ProductionPlanService _productionPlanService;
    private readonly ITestOutputHelper _output;

    public ProductionPlanQueueTest(ITestOutputHelper output)
    {
        _output = output;
        _productionPlanService = ServiceProvider.GetService<ProductionPlanService>();
    }

    /// <summary>
    /// 测试异步分解生产计划
    /// </summary>
    [Fact]
    public async Task DecomposeProductionPlanAsyncQueue_ShouldSucceed()
    {
        // Arrange
        var input = new AsyncDecomposeProductionPlanInput
        {
            PlanId = 1, // 假设存在ID为1的未分解计划
            Priority = 5,
            DecomposeType = DecomposeType.Full,
            UseReliableQueue = false,
            MaxRetryCount = 3,
            TimeoutMinutes = 30,
            Options = new DecomposeOptions
            {
                GenerateMaterialWorkOrder = true,
                GenerateSubProductWorkOrder = true,
                GenerateMainBomWorkOrder = true,
                ParallelProcessing = true
            }
        };

        // Act
        var result = await _productionPlanService.DecomposeProductionPlanAsyncQueue(input);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.MessageId);
        Assert.Equal(input.PlanId, result.PlanId);
        Assert.True(result.EstimatedProcessTime > 0);
        
        _output.WriteLine($"异步分解任务提交成功:");
        _output.WriteLine($"  消息ID: {result.MessageId}");
        _output.WriteLine($"  计划ID: {result.PlanId}");
        _output.WriteLine($"  预估处理时间: {result.EstimatedProcessTime}分钟");
        _output.WriteLine($"  提交时间: {result.SubmitTime}");
    }

    /// <summary>
    /// 测试批量异步分解生产计划
    /// </summary>
    [Fact]
    public async Task BatchDecomposeProductionPlanAsync_ShouldSucceed()
    {
        // Arrange
        var input = new BatchAsyncDecomposeInput
        {
            PlanIds = new List<long> { 1, 2, 3 }, // 假设存在这些计划
            Priority = 3,
            DecomposeType = DecomposeType.Full,
            UseReliableQueue = true,
            Options = new DecomposeOptions
            {
                GenerateMaterialWorkOrder = true,
                GenerateSubProductWorkOrder = false,
                CheckInventory = true
            }
        };

        // Act
        var result = await _productionPlanService.BatchDecomposeProductionPlanAsync(input);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(input.PlanIds.Count, result.TotalCount);
        Assert.True(result.SuccessCount >= 0);
        Assert.True(result.FailedCount >= 0);
        Assert.Equal(result.TotalCount, result.SuccessCount + result.FailedCount);
        
        _output.WriteLine($"批量异步分解结果:");
        _output.WriteLine($"  总数: {result.TotalCount}");
        _output.WriteLine($"  成功: {result.SuccessCount}");
        _output.WriteLine($"  失败: {result.FailedCount}");
        _output.WriteLine($"  消息: {result.Message}");

        // 输出成功的任务详情
        foreach (var success in result.SuccessResults)
        {
            _output.WriteLine($"  成功任务 - 计划ID: {success.PlanId}, 消息ID: {success.MessageId}");
        }

        // 输出失败的任务详情
        foreach (var failed in result.FailedResults)
        {
            _output.WriteLine($"  失败任务 - 计划ID: {failed.PlanId}, 错误: {failed.Message}");
        }
    }

    /// <summary>
    /// 测试获取队列状态
    /// </summary>
    [Fact]
    public async Task GetDecomposeQueueStatus_ShouldReturnStatus()
    {
        // Act
        var status = await _productionPlanService.GetDecomposeQueueStatus();

        // Assert
        Assert.NotNull(status);
        Assert.True(status.DecomposeQueueCount >= 0);
        Assert.True(status.StatusUpdateQueueCount >= 0);
        Assert.True(status.WorkOrderQueueCount >= 0);
        
        _output.WriteLine($"队列状态信息:");
        _output.WriteLine($"  分解队列消息数: {status.DecomposeQueueCount}");
        _output.WriteLine($"  状态更新队列消息数: {status.StatusUpdateQueueCount}");
        _output.WriteLine($"  工单队列消息数: {status.WorkOrderQueueCount}");
        _output.WriteLine($"  最后更新时间: {status.LastUpdateTime}");
        
        if (!string.IsNullOrEmpty(status.ErrorMessage))
        {
            _output.WriteLine($"  错误信息: {status.ErrorMessage}");
        }
    }

    /// <summary>
    /// 测试消息队列发布功能
    /// </summary>
    [Fact]
    public async Task PublishDecomposeMessage_ShouldSucceed()
    {
        // Arrange
        var message = new ProductionPlanDecomposeMessage
        {
            PlanId = 999,
            PlanCode = "TEST-PLAN-001",
            PlanName = "测试生产计划",
            BomId = 1,
            PlanQuantity = 100,
            Priority = 4,
            DecomposeType = DecomposeType.Full,
            CreateUserId = 1,
            CreateUserName = "测试用户"
        };

        // Act
        var result = await ProductionPlanDecomposeQueue.PublishDecomposeTaskAsync(message);

        // Assert
        Assert.True(result);
        
        _output.WriteLine($"消息发布成功:");
        _output.WriteLine($"  消息ID: {message.MessageId}");
        _output.WriteLine($"  计划ID: {message.PlanId}");
        _output.WriteLine($"  计划编号: {message.PlanCode}");
        _output.WriteLine($"  优先级: {message.Priority}");
    }

    /// <summary>
    /// 测试可信队列发布功能
    /// </summary>
    [Fact]
    public async Task PublishReliableDecomposeMessage_ShouldSucceed()
    {
        // Arrange
        var message = new ProductionPlanDecomposeMessage
        {
            PlanId = 888,
            PlanCode = "RELIABLE-PLAN-001",
            PlanName = "可信队列测试计划",
            BomId = 2,
            PlanQuantity = 50,
            Priority = 5,
            DecomposeType = DecomposeType.MaterialOnly,
            CreateUserId = 1,
            CreateUserName = "测试用户"
        };

        // Act
        var result = await ProductionPlanDecomposeQueue.PublishReliableDecomposeTaskAsync(message);

        // Assert
        Assert.True(result);
        
        _output.WriteLine($"可信队列消息发布成功:");
        _output.WriteLine($"  消息ID: {message.MessageId}");
        _output.WriteLine($"  计划ID: {message.PlanId}");
        _output.WriteLine($"  分解类型: {message.DecomposeType}");
    }

    /// <summary>
    /// 测试状态更新消息发布
    /// </summary>
    [Fact]
    public async Task PublishStatusUpdateMessage_ShouldSucceed()
    {
        // Arrange
        var message = new ProductionPlanStatusMessage
        {
            PlanId = 777,
            OldStatus = Admin.NET.Core.Enum.ProductionPlanStatusEnum.Undecomposed,
            NewStatus = Admin.NET.Core.Enum.ProductionPlanStatusEnum.Decomposing,
            Reason = "开始分解测试",
            OperatorUserId = 1,
            OperatorUserName = "测试用户"
        };

        // Act
        var result = await ProductionPlanDecomposeQueue.PublishStatusUpdateAsync(message);

        // Assert
        Assert.True(result);
        
        _output.WriteLine($"状态更新消息发布成功:");
        _output.WriteLine($"  消息ID: {message.MessageId}");
        _output.WriteLine($"  计划ID: {message.PlanId}");
        _output.WriteLine($"  状态变更: {message.OldStatus} -> {message.NewStatus}");
        _output.WriteLine($"  变更原因: {message.Reason}");
    }

    /// <summary>
    /// 测试工单生成消息发布
    /// </summary>
    [Fact]
    public async Task PublishWorkOrderMessage_ShouldSucceed()
    {
        // Arrange
        var message = new WorkOrderGenerationMessage
        {
            PlanId = 666,
            BomId = 3,
            WorkOrderType = WorkOrderType.Material,
            MaterialId = 100,
            MaterialCode = "MAT-001",
            MaterialName = "测试物料",
            RequiredQuantity = 200,
            Priority = 3,
            CreateUserId = 1
        };

        // Act
        var result = await ProductionPlanDecomposeQueue.PublishWorkOrderGenerationAsync(message);

        // Assert
        Assert.True(result);
        
        _output.WriteLine($"工单生成消息发布成功:");
        _output.WriteLine($"  消息ID: {message.MessageId}");
        _output.WriteLine($"  计划ID: {message.PlanId}");
        _output.WriteLine($"  工单类型: {message.WorkOrderType}");
        _output.WriteLine($"  物料: {message.MaterialName} ({message.MaterialCode})");
        _output.WriteLine($"  需求数量: {message.RequiredQuantity}");
    }

    /// <summary>
    /// 测试延迟重试消息发布
    /// </summary>
    [Fact]
    public async Task PublishDelayRetryMessage_ShouldSucceed()
    {
        // Arrange
        var message = new ProductionPlanDecomposeMessage
        {
            PlanId = 555,
            PlanCode = "RETRY-PLAN-001",
            PlanName = "重试测试计划",
            RetryCount = 1,
            MaxRetryCount = 3
        };

        // Act
        var result = await ProductionPlanDecomposeQueue.PublishDelayRetryAsync(message, 30);

        // Assert
        Assert.True(result);
        
        _output.WriteLine($"延迟重试消息发布成功:");
        _output.WriteLine($"  消息ID: {message.MessageId}");
        _output.WriteLine($"  计划ID: {message.PlanId}");
        _output.WriteLine($"  重试次数: {message.RetryCount}/{message.MaxRetryCount}");
        _output.WriteLine($"  延迟时间: 30秒");
    }
}
