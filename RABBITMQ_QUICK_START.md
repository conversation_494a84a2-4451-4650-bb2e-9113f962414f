# RabbitMQ 生产计划分解系统快速开始

## 🚀 快速开始

### 1. 启动 RabbitMQ 服务

#### 使用 Docker（推荐）
```bash
# 启动 RabbitMQ 容器
docker run -d --name rabbitmq \
  -p 5672:5672 \
  -p 15672:15672 \
  -e RABBITMQ_DEFAULT_USER=guest \
  -e RABBITMQ_DEFAULT_PASS=guest \
  rabbitmq:3-management

# 检查容器状态
docker ps | grep rabbitmq
```

#### 本地安装
1. 安装 Erlang OTP
2. 安装 RabbitMQ Server
3. 启用管理插件：`rabbitmq-plugins enable rabbitmq_management`
4. 访问管理界面：http://localhost:15672 (guest/guest)

### 2. 配置应用程序

确保 `Admin.NET.Application/Configuration/EventBus.json` 配置正确：

```json
{
  "EventBus": {
    "EventSourceType": "RabbitMQ",
    "RabbitMQ": {
      "UserName": "guest",
      "Password": "guest",
      "HostName": "127.0.0.1",
      "Port": 5672
    }
  }
}
```

### 3. 启动应用程序

```bash
cd Admin.NET.Web.Entry
dotnet run
```

### 4. 测试 API

#### 4.1 异步分解单个生产计划

```bash
curl -X POST "http://localhost:5000/api/productionPlan/decomposeProductionPlanAsyncRabbitMQ" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "planId": 1,
    "priority": 5,
    "decomposeType": 1,
    "maxRetryCount": 3,
    "timeoutMinutes": 30,
    "options": {
      "generateMaterialWorkOrder": true,
      "generateSubProductWorkOrder": true,
      "generateMainBomWorkOrder": true,
      "checkInventory": false,
      "autoSchedule": false,
      "parallelProcessing": true
    }
  }'
```

**预期响应：**
```json
{
  "success": true,
  "message": "分解任务已提交到队列",
  "messageId": "550e8400-e29b-41d4-a716-446655440000",
  "planId": 1,
  "submitTime": "2024-01-15T10:30:00",
  "estimatedProcessTime": 5
}
```

#### 4.2 批量异步分解

```bash
curl -X POST "http://localhost:5000/api/productionPlan/batchDecomposeProductionPlanAsyncRabbitMQ" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "planIds": [1, 2, 3],
    "priority": 3,
    "decomposeType": 1,
    "maxRetryCount": 3,
    "options": {
      "generateMaterialWorkOrder": true,
      "generateSubProductWorkOrder": true
    }
  }'
```

#### 4.3 查询队列状态

```bash
curl -X GET "http://localhost:5000/api/productionPlan/getRabbitMQQueueStatus" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**预期响应：**
```json
{
  "isConnected": true,
  "decomposeQueueCount": 5,
  "statusUpdateQueueCount": 2,
  "workOrderQueueCount": 3,
  "retryQueueCount": 1,
  "deadLetterQueueCount": 0,
  "totalMessageCount": 11,
  "lastUpdateTime": "2024-01-15T10:30:00",
  "errorMessage": null
}
```

## 📊 监控和调试

### 1. RabbitMQ 管理界面

访问 http://localhost:15672 查看：
- 队列状态
- 消息数量
- 消费者连接
- 交换机绑定

### 2. 应用程序日志

查看应用程序日志中的 RabbitMQ 相关信息：

```bash
# 查看实时日志
tail -f logs/application.log | grep -i rabbitmq

# 查看分解任务日志
tail -f logs/application.log | grep "生产计划分解"
```

### 3. 队列信息

系统会自动创建以下队列：
- `production_plan_decompose`: 分解任务队列
- `production_plan_status_update`: 状态更新队列
- `production_plan_work_order`: 工单生成队列
- `production_plan_decompose_retry`: 重试队列
- `production_plan_decompose_dlq`: 死信队列

## 🔧 故障排查

### 常见问题

1. **RabbitMQ 连接失败**
   ```
   错误: None of the specified endpoints were reachable
   解决: 
   - 检查 RabbitMQ 服务是否启动
   - 检查端口 5672 是否开放
   - 验证用户名密码是否正确
   ```

2. **消费者未启动**
   ```
   现象: 队列中有消息但不被处理
   解决:
   - 检查应用程序日志
   - 确认 ProductionPlanRabbitMQConsumer 服务已注册
   - 重启应用程序
   ```

3. **消息进入死信队列**
   ```
   现象: production_plan_decompose_dlq 队列中有消息
   解决:
   - 查看应用程序错误日志
   - 检查生产计划数据完整性
   - 修复问题后手动重新处理
   ```

### 调试步骤

1. **检查 RabbitMQ 连接**
   ```bash
   # 测试连接
   telnet localhost 5672
   ```

2. **检查队列状态**
   - 访问 RabbitMQ 管理界面
   - 查看队列消息数量
   - 检查消费者连接状态

3. **查看应用程序日志**
   ```bash
   # 查看启动日志
   grep "RabbitMQ" logs/application.log
   
   # 查看错误日志
   grep "ERROR" logs/application.log | grep -i rabbitmq
   ```

## 📈 性能优化

### 1. 消费者配置

```csharp
// 调整预取数量
await channel.BasicQosAsync(0, 10, false); // 增加到 10

// 增加消费者实例
services.AddHostedService<ProductionPlanRabbitMQConsumer>();
services.AddHostedService<ProductionPlanRabbitMQConsumer>(); // 第二个实例
```

### 2. 连接池优化

```csharp
var factory = new ConnectionFactory
{
    UserName = "guest",
    Password = "guest",
    HostName = "127.0.0.1",
    Port = 5672,
    VirtualHost = "/",
    AutomaticRecoveryEnabled = true,
    NetworkRecoveryInterval = TimeSpan.FromSeconds(10),
    RequestedHeartbeat = TimeSpan.FromSeconds(60),
    RequestedConnectionTimeout = TimeSpan.FromSeconds(30)
};
```

### 3. 批量处理

```csharp
// 批量发布消息
var messages = new List<ProductionPlanDecomposeRabbitMQMessage>();
// ... 添加消息

foreach (var message in messages)
{
    await ProductionPlanRabbitMQManager.PublishDecomposeTaskAsync(message);
}
```

## 🎯 最佳实践

1. **合理设置优先级**
   - 紧急订单：优先级 5
   - 重要客户：优先级 4
   - 常规订单：优先级 3
   - 补充库存：优先级 2
   - 测试任务：优先级 1

2. **监控队列积压**
   - 定期检查队列消息数量
   - 设置告警阈值
   - 及时扩容消费者

3. **错误处理**
   - 监控死信队列
   - 及时处理失败消息
   - 分析失败原因

4. **性能测试**
   - 压力测试分解能力
   - 监控系统资源使用
   - 优化处理逻辑

## 📝 开发指南

### 添加新的消息类型

1. 在 `ProductionPlanRabbitMQMessages.cs` 中定义消息类
2. 在 `ProductionPlanRabbitMQManager.cs` 中添加发布方法
3. 在 `ProductionPlanRabbitMQConsumer.cs` 中添加消费逻辑
4. 更新队列初始化代码

### 扩展分解选项

1. 修改 `DecomposeOptions` 类
2. 更新消费者处理逻辑
3. 添加相应的业务逻辑

### 集成其他系统

```csharp
// 发布自定义消息
await ProductionPlanRabbitMQManager.PublishWorkOrderGenerationAsync(new WorkOrderGenerationRabbitMQMessage
{
    PlanId = planId,
    WorkOrderType = WorkOrderType.Custom,
    ExtendData = new Dictionary<string, object>
    {
        { "customField", "customValue" }
    }
});
```

通过以上配置和测试，您的基于 RabbitMQ 的生产计划分解系统就可以正常运行了！🎉
