// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using LangChain.Providers.OpenAI;
using LangChain.Providers;
using System.Text.Json;

namespace Admin.NET.Core.Service;

/// <summary>
/// AI知识库服务 🧩
/// </summary>
[ApiDescriptionSettings(Order = 381)]
public class SysAiKnowledgeService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<SysAiKnowledge> _sysAiKnowledgeRep;
    private readonly UserManager _userManager;
    private readonly IOptions<AiChatOptions> _aiChatOptions;
    private readonly ILogger<SysAiKnowledgeService> _logger;

    public SysAiKnowledgeService(
        SqlSugarRepository<SysAiKnowledge> sysAiKnowledgeRep,
        UserManager userManager,
        IOptions<AiChatOptions> aiChatOptions,
        ILogger<SysAiKnowledgeService> logger)
    {
        _sysAiKnowledgeRep = sysAiKnowledgeRep;
        _userManager = userManager;
        _aiChatOptions = aiChatOptions;
        _logger = logger;
    }

    /// <summary>
    /// 获取AI知识库分页列表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取AI知识库分页列表")]
    public async Task<SqlSugarPagedList<SysAiKnowledge>> GetAiKnowledgePage(PageAiKnowledgeInput input)
    {
        return await _sysAiKnowledgeRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Title), u => u.Title.Contains(input.Title))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Category), u => u.Category.Contains(input.Category))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keywords), u => u.Keywords.Contains(input.Keywords))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Source), u => u.Source.Contains(input.Source))
            .WhereIF(input.Status.HasValue, u => u.Status == (int)input.Status)
            .OrderByDescending(u => u.Priority)
            .ThenByDescending(u => u.UsageCount)
            .ThenByDescending(u => u.CreateTime)
            .ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加AI知识库 📢
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加AI知识库")]
    public async Task<long> AddAiKnowledge(AddAiKnowledgeInput input)
    {
        var entity = input.Adapt<SysAiKnowledge>();
        entity.TenantId = _userManager.TenantId;
        
        // 生成向量嵌入
        if (!string.IsNullOrWhiteSpace(entity.Content))
        {
            entity.Embedding = await GenerateEmbedding(entity.Content);
        }

        var newEntity = await _sysAiKnowledgeRep.InsertReturnEntityAsync(entity);
        return newEntity.Id;
    }

    /// <summary>
    /// 更新AI知识库 📢
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新AI知识库")]
    public async Task UpdateAiKnowledge(UpdateAiKnowledgeInput input)
    {
        var entity = input.Adapt<SysAiKnowledge>();
        
        // 如果内容发生变化，重新生成向量嵌入
        var existingEntity = await _sysAiKnowledgeRep.GetByIdAsync(entity.Id);
        if (existingEntity != null && existingEntity.Content != entity.Content)
        {
            entity.Embedding = await GenerateEmbedding(entity.Content);
        }

        await _sysAiKnowledgeRep.UpdateAsync(entity);
    }

    /// <summary>
    /// 删除AI知识库 📢
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [DisplayName("删除AI知识库")]
    public async Task DeleteAiKnowledge(DeleteAiKnowledgeInput input)
    {
        await _sysAiKnowledgeRep.DeleteByIdAsync(input.Id);
    }

    /// <summary>
    /// 获取AI知识库详情 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取AI知识库详情")]
    public async Task<SysAiKnowledge> GetAiKnowledge([FromQuery] BaseIdInput input)
    {
        return await _sysAiKnowledgeRep.GetByIdAsync(input.Id);
    }

    /// <summary>
    /// 搜索知识库 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("搜索知识库")]
    public async Task<List<KnowledgeSearchResult>> SearchKnowledge(SearchKnowledgeInput input)
    {
        var results = new List<KnowledgeSearchResult>();

        try
        {
            if (input.SearchType == "semantic" && !string.IsNullOrWhiteSpace(_aiChatOptions.Value.OpenAiApiKey))
            {
                // 语义搜索
                results = await SemanticSearch(input);
            }
            else
            {
                // 关键词搜索
                results = await KeywordSearch(input);
            }

            // 更新使用统计
            await UpdateUsageStatistics(results.Select(r => r.Id).ToList());

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "知识库搜索失败 - 查询:{Query}", input.Query);
            
            // 降级到关键词搜索
            if (input.SearchType == "semantic")
            {
                return await KeywordSearch(input);
            }
            
            throw;
        }
    }

    /// <summary>
    /// 批量导入知识库 📢
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Import"), HttpPost]
    [DisplayName("批量导入知识库")]
    public async Task<int> ImportKnowledge(ImportKnowledgeInput input)
    {
        var importCount = 0;
        var tenantId = _userManager.TenantId;

        foreach (var item in input.KnowledgeList)
        {
            try
            {
                // 检查是否已存在
                if (!input.OverwriteExisting)
                {
                    var exists = await _sysAiKnowledgeRep.IsAnyAsync(u => u.Title == item.Title && u.TenantId == tenantId);
                    if (exists)
                    {
                        _logger.LogWarning("知识库条目已存在，跳过：{Title}", item.Title);
                        continue;
                    }
                }

                var entity = new SysAiKnowledge
                {
                    Title = item.Title,
                    Content = item.Content,
                    Category = !string.IsNullOrWhiteSpace(item.Category) ? item.Category : input.DefaultCategory,
                    Keywords = item.Keywords,
                    Source = !string.IsNullOrWhiteSpace(item.Source) ? item.Source : input.DefaultSource,
                    Priority = item.Priority,
                    Status = (int)AiKnowledgeStatusEnum.Enabled,
                    TenantId = tenantId
                };

                // 生成向量嵌入
                entity.Embedding = await GenerateEmbedding(entity.Content);

                if (input.OverwriteExisting)
                {
                    var existing = await _sysAiKnowledgeRep.GetFirstAsync(u => u.Title == item.Title && u.TenantId == tenantId);
                    if (existing != null)
                    {
                        entity.Id = existing.Id;
                        await _sysAiKnowledgeRep.UpdateAsync(entity);
                    }
                    else
                    {
                        await _sysAiKnowledgeRep.InsertAsync(entity);
                    }
                }
                else
                {
                    await _sysAiKnowledgeRep.InsertAsync(entity);
                }

                importCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入知识库条目失败：{Title}", item.Title);
            }
        }

        _logger.LogInformation("知识库批量导入完成，成功导入 {Count} 条", importCount);
        return importCount;
    }

    /// <summary>
    /// 获取知识库分类列表 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取知识库分类列表")]
    public async Task<List<string>> GetKnowledgeCategories()
    {
        return await _sysAiKnowledgeRep.AsQueryable()
            .Where(u => u.Status == (int)AiKnowledgeStatusEnum.Enabled)
            .Where(u => !string.IsNullOrEmpty(u.Category))
            .GroupBy(u => u.Category)
            .Select(u => u.Key)
            .ToListAsync();
    }

    #region 私有方法

    /// <summary>
    /// 生成文本向量嵌入
    /// </summary>
    private async Task<string> GenerateEmbedding(string text)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(_aiChatOptions.Value.OpenAiApiKey))
            {
                _logger.LogWarning("OpenAI API密钥未配置，跳过向量嵌入生成");
                return null;
            }

            // 这里应该调用OpenAI的嵌入API
            // 由于LangChain.NET的API可能会变化，这里提供一个基础实现框架
            // 实际使用时需要根据LangChain.NET的最新API进行调整

            // 暂时返回空，实际项目中需要实现真正的嵌入生成
            await Task.Delay(1); // 模拟异步操作
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成向量嵌入失败：{Text}", text.Substring(0, Math.Min(100, text.Length)));
            return null;
        }
    }

    /// <summary>
    /// 语义搜索
    /// </summary>
    private async Task<List<KnowledgeSearchResult>> SemanticSearch(SearchKnowledgeInput input)
    {
        // 生成查询向量
        var queryEmbedding = await GenerateEmbedding(input.Query);
        if (string.IsNullOrWhiteSpace(queryEmbedding))
        {
            // 降级到关键词搜索
            return await KeywordSearch(input);
        }

        // 这里应该实现向量相似度计算
        // 由于需要复杂的向量计算，暂时降级到关键词搜索
        return await KeywordSearch(input);
    }

    /// <summary>
    /// 关键词搜索
    /// </summary>
    private async Task<List<KnowledgeSearchResult>> KeywordSearch(SearchKnowledgeInput input)
    {
        var query = _sysAiKnowledgeRep.AsQueryable()
            .Where(u => u.Status == (int)AiKnowledgeStatusEnum.Enabled);

        if (input.OnlyEnabled)
        {
            query = query.Where(u => u.Status == (int)AiKnowledgeStatusEnum.Enabled);
        }

        if (!string.IsNullOrWhiteSpace(input.Category))
        {
            query = query.Where(u => u.Category == input.Category);
        }

        // 关键词匹配
        var keywords = input.Query.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        foreach (var keyword in keywords)
        {
            query = query.Where(u => u.Title.Contains(keyword) ||
                                   u.Content.Contains(keyword) ||
                                   u.Keywords.Contains(keyword));
        }

        var results = await query
            .OrderByDescending(u => u.Priority)
            .ThenByDescending(u => u.UsageCount)
            .Take(input.MaxResults)
            .Select(u => new KnowledgeSearchResult
            {
                Id = u.Id,
                Title = u.Title,
                Content = u.Content,
                Category = u.Category,
                Keywords = u.Keywords,
                Score = 1.0, // 关键词搜索暂时设为固定分数
                UsageCount = u.UsageCount
            })
            .ToListAsync();

        return results;
    }

    /// <summary>
    /// 更新使用统计
    /// </summary>
    private async Task UpdateUsageStatistics(List<long> knowledgeIds)
    {
        if (!knowledgeIds.Any()) return;

        try
        {
            await _sysAiKnowledgeRep.UpdateAsync(
                u => knowledgeIds.Contains(u.Id),
                u => new SysAiKnowledge
                {
                    UsageCount = u.UsageCount + 1,
                    LastUsedTime = DateTime.Now
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新知识库使用统计失败");
        }
    }

    #endregion
}
