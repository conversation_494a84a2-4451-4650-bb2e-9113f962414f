// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core;

/// <summary>
/// AI配置种子数据
/// </summary>
public class SysAiConfigSeedData : ISqlSugarEntitySeedData<SysAiConfig>
{
    /// <summary>
    /// 种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysAiConfig> HasData()
    {
        return new[]
        {
            new SysAiConfig
            {
                Id = 1500000000001,
                Name = "OpenAI API密钥",
                ConfigKey = "OpenAiApiKey",
                ConfigValue = "",
                ConfigType = "string",
                GroupName = "AI模型配置",
                Description = "OpenAI API的访问密钥，用于调用GPT模型",
                IsEnabled = true,
                IsSystem = true,
                Sort = 1,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            },
            new SysAiConfig
            {
                Id = 1500000000002,
                Name = "OpenAI API基础URL",
                ConfigKey = "OpenAiBaseUrl",
                ConfigValue = "https://api.openai.com/v1",
                ConfigType = "string",
                GroupName = "AI模型配置",
                Description = "OpenAI API的基础URL地址",
                IsEnabled = true,
                IsSystem = true,
                Sort = 2,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            },
            new SysAiConfig
            {
                Id = 1500000000003,
                Name = "默认AI模型",
                ConfigKey = "DefaultModel",
                ConfigValue = "gpt-3.5-turbo",
                ConfigType = "string",
                GroupName = "AI模型配置",
                Description = "默认使用的AI模型名称",
                IsEnabled = true,
                IsSystem = true,
                Sort = 3,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            },
            new SysAiConfig
            {
                Id = 1500000000004,
                Name = "最大Token数",
                ConfigKey = "MaxTokens",
                ConfigValue = "2000",
                ConfigType = "int",
                GroupName = "AI模型配置",
                Description = "AI回复的最大Token数量",
                IsEnabled = true,
                IsSystem = true,
                Sort = 4,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            },
            new SysAiConfig
            {
                Id = 1500000000005,
                Name = "温度参数",
                ConfigKey = "Temperature",
                ConfigValue = "0.7",
                ConfigType = "double",
                GroupName = "AI模型配置",
                Description = "控制AI回复随机性的温度参数（0-2之间）",
                IsEnabled = true,
                IsSystem = true,
                Sort = 5,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            },
            new SysAiConfig
            {
                Id = 1500000000006,
                Name = "系统提示词",
                ConfigKey = "SystemPrompt",
                ConfigValue = "你是一个专业的MES系统智能助手，专门帮助用户解决制造执行系统相关的问题。你了解生产计划、工单管理、设备监控、质量控制、库存管理等MES核心功能。请用专业、友好的语气回答用户的问题，并尽可能提供具体的解决方案。",
                ConfigType = "text",
                GroupName = "AI行为配置",
                Description = "AI助手的系统提示词，定义AI的角色和行为",
                IsEnabled = true,
                IsSystem = true,
                Sort = 10,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            },
            new SysAiConfig
            {
                Id = 1500000000007,
                Name = "最大对话历史长度",
                ConfigKey = "MaxHistoryLength",
                ConfigValue = "20",
                ConfigType = "int",
                GroupName = "AI行为配置",
                Description = "AI记忆的最大对话历史条数",
                IsEnabled = true,
                IsSystem = true,
                Sort = 11,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            },
            new SysAiConfig
            {
                Id = 1500000000008,
                Name = "启用知识库检索",
                ConfigKey = "EnableKnowledgeRetrieval",
                ConfigValue = "true",
                ConfigType = "bool",
                GroupName = "知识库配置",
                Description = "是否启用知识库检索功能",
                IsEnabled = true,
                IsSystem = true,
                Sort = 20,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            },
            new SysAiConfig
            {
                Id = 1500000000009,
                Name = "知识库相似度阈值",
                ConfigKey = "KnowledgeSimilarityThreshold",
                ConfigValue = "0.7",
                ConfigType = "double",
                GroupName = "知识库配置",
                Description = "知识库检索的相似度阈值（0-1之间）",
                IsEnabled = true,
                IsSystem = true,
                Sort = 21,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            },
            new SysAiConfig
            {
                Id = 1500000000010,
                Name = "最大知识库检索数量",
                ConfigKey = "MaxKnowledgeRetrievalCount",
                ConfigValue = "5",
                ConfigType = "int",
                GroupName = "知识库配置",
                Description = "单次检索返回的最大知识库条目数量",
                IsEnabled = true,
                IsSystem = true,
                Sort = 22,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            },
            new SysAiConfig
            {
                Id = 1500000000011,
                Name = "启用内容过滤",
                ConfigKey = "EnableContentFilter",
                ConfigValue = "true",
                ConfigType = "bool",
                GroupName = "安全配置",
                Description = "是否启用敏感内容过滤",
                IsEnabled = true,
                IsSystem = true,
                Sort = 30,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            },
            new SysAiConfig
            {
                Id = 1500000000012,
                Name = "敏感词列表",
                ConfigKey = "SensitiveWords",
                ConfigValue = "[\"政治\",\"暴力\",\"色情\"]",
                ConfigType = "json",
                GroupName = "安全配置",
                Description = "敏感词列表，JSON格式",
                IsEnabled = true,
                IsSystem = true,
                Sort = 31,
                TenantId = 1300000000001,
                CreateTime = DateTime.Parse("2024-01-01 00:00:00"),
                UpdateTime = DateTime.Parse("2024-01-01 00:00:00")
            }
        };
    }
}
