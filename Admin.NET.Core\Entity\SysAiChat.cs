// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core;

/// <summary>
/// AI聊天会话实体
/// </summary>
[SugarTable(null, "AI聊天会话")]
[SysTable]
public class SysAiChat : EntityBase
{
    /// <summary>
    /// 会话标题
    /// </summary>
    [SugarColumn(ColumnDescription = "会话标题", Length = 200)]
    [Required, MaxLength(200)]
    public string Title { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    [SugarColumn(ColumnDescription = "用户ID")]
    public long UserId { get; set; }

    /// <summary>
    /// 用户名称
    /// </summary>
    [SugarColumn(ColumnDescription = "用户名称", Length = 50)]
    [MaxLength(50)]
    public string UserName { get; set; }

    /// <summary>
    /// 会话状态（0：进行中，1：已结束）
    /// </summary>
    [SugarColumn(ColumnDescription = "会话状态")]
    public int Status { get; set; } = 0;

    /// <summary>
    /// 最后消息时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后消息时间")]
    public DateTime? LastMessageTime { get; set; }

    /// <summary>
    /// 消息数量
    /// </summary>
    [SugarColumn(ColumnDescription = "消息数量")]
    public int MessageCount { get; set; } = 0;

    /// <summary>
    /// 会话摘要
    /// </summary>
    [SugarColumn(ColumnDescription = "会话摘要", Length = 500)]
    [MaxLength(500)]
    public string Summary { get; set; }

    /// <summary>
    /// 租户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "租户Id")]
    public virtual long TenantId { get; set; }
}
