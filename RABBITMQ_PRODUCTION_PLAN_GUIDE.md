# 基于 RabbitMQ 的生产计划分解系统

## 📋 概述

本系统将原有的同步生产计划分解功能改造为基于 RabbitMQ 的异步消息队列系统，提供高可靠性、高性能的分解处理能力。

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端/API      │    │   RabbitMQ      │    │   消费者服务     │
│                 │    │                 │    │                 │
│ 提交分解任务     │───▶│ 分解队列         │───▶│ 异步处理分解     │
│ 查询队列状态     │    │ 状态更新队列     │    │ - 状态更新       │
│ 批量分解        │    │ 工单生成队列     │    │ - 工单生成       │
│                 │    │ 重试队列         │    │ - 重试机制       │
│                 │    │ 死信队列         │    │ - 错误处理       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 核心功能

### 1. 异步分解处理
- **同步分解**: 传统的同步分解方式，适合小批量处理
- **异步分解**: 基于 RabbitMQ 的异步分解，适合大批量和高并发场景
- **批量分解**: 支持一次性提交多个生产计划进行分解

### 2. 消息队列类型
- **分解队列**: 处理生产计划分解任务
- **状态更新队列**: 处理计划状态变更
- **工单生成队列**: 处理工单创建任务
- **重试队列**: 处理失败任务的重试
- **死信队列**: 处理无法恢复的失败消息

### 3. 可靠性保证
- **消息持久化**: 确保消息不丢失
- **自动重试**: 指数退避重试机制
- **死信处理**: 失败消息进入死信队列
- **手动确认**: 确保消息被正确处理

## 📝 API 接口

### 1. RabbitMQ 异步分解生产计划

```http
POST /api/productionPlan/decomposeProductionPlanAsyncRabbitMQ
Content-Type: application/json

{
  "planId": 123,
  "priority": 5,
  "decomposeType": 1,
  "maxRetryCount": 3,
  "timeoutMinutes": 30,
  "options": {
    "generateMaterialWorkOrder": true,
    "generateSubProductWorkOrder": true,
    "generateMainBomWorkOrder": true,
    "checkInventory": false,
    "autoSchedule": false,
    "parallelProcessing": true
  }
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "分解任务已提交到队列",
  "messageId": "550e8400-e29b-41d4-a716-446655440000",
  "planId": 123,
  "submitTime": "2024-01-15T10:30:00",
  "estimatedProcessTime": 5
}
```

### 2. 批量 RabbitMQ 异步分解

```http
POST /api/productionPlan/batchDecomposeProductionPlanAsyncRabbitMQ
Content-Type: application/json

{
  "planIds": [123, 124, 125],
  "priority": 3,
  "decomposeType": 1,
  "maxRetryCount": 3,
  "options": {
    "generateMaterialWorkOrder": true,
    "generateSubProductWorkOrder": true
  }
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "成功提交3个分解任务，0个失败",
  "totalCount": 3,
  "successCount": 3,
  "failedCount": 0,
  "successResults": [
    {
      "success": true,
      "messageId": "550e8400-e29b-41d4-a716-446655440001",
      "planId": 123,
      "estimatedProcessTime": 3
    }
  ],
  "failedResults": []
}
```

### 3. 获取 RabbitMQ 队列状态

```http
GET /api/productionPlan/getRabbitMQQueueStatus
```

**响应示例:**
```json
{
  "isConnected": true,
  "decomposeQueueCount": 5,
  "statusUpdateQueueCount": 2,
  "workOrderQueueCount": 3,
  "retryQueueCount": 1,
  "deadLetterQueueCount": 0,
  "totalMessageCount": 11,
  "lastUpdateTime": "2024-01-15T10:30:00",
  "errorMessage": null
}
```

## 🔧 配置说明

### 1. RabbitMQ 连接配置

在 `Admin.NET.Application/Configuration/EventBus.json` 中配置：

```json
{
  "EventBus": {
    "EventSourceType": "RabbitMQ",
    "RabbitMQ": {
      "UserName": "guest",
      "Password": "guest",
      "HostName": "127.0.0.1",
      "Port": 5672
    }
  }
}
```

### 2. 分解选项配置

```csharp
public class DecomposeOptions
{
    public bool GenerateMaterialWorkOrder { get; set; } = true;     // 生成物料工单
    public bool GenerateSubProductWorkOrder { get; set; } = true;   // 生成半成品工单
    public bool GenerateMainBomWorkOrder { get; set; } = true;      // 生成主BOM工单
    public bool CheckInventory { get; set; } = false;               // 检查库存
    public bool AutoSchedule { get; set; } = false;                 // 自动排产
    public bool ParallelProcessing { get; set; } = true;            // 并行处理
    public int DecomposeDepth { get; set; } = -1;                   // 分解深度(-1无限)
    public int BatchSize { get; set; } = 100;                       // 批处理大小
}
```

## 🛠️ 部署和运维

### 1. RabbitMQ 安装和配置

#### Windows 环境

1. **安装 Erlang**:
   ```bash
   # 下载并安装 Erlang OTP
   # https://www.erlang.org/downloads
   ```

2. **安装 RabbitMQ**:
   ```bash
   # 下载并安装 RabbitMQ Server
   # https://www.rabbitmq.com/download.html
   ```

3. **启用管理插件**:
   ```bash
   rabbitmq-plugins enable rabbitmq_management
   ```

4. **访问管理界面**:
   ```
   http://localhost:15672
   用户名: guest
   密码: guest
   ```

#### Docker 环境

```bash
# 运行 RabbitMQ 容器
docker run -d --name rabbitmq \
  -p 5672:5672 \
  -p 15672:15672 \
  -e RABBITMQ_DEFAULT_USER=admin \
  -e RABBITMQ_DEFAULT_PASS=admin123 \
  rabbitmq:3-management
```

### 2. 队列监控

系统会自动创建以下队列：

- `production_plan_decompose`: 分解任务队列
- `production_plan_status_update`: 状态更新队列
- `production_plan_work_order`: 工单生成队列
- `production_plan_decompose_retry`: 重试队列
- `production_plan_decompose_dlq`: 死信队列

### 3. 性能调优

#### 消费者配置
```csharp
// 设置预取数量
await channel.BasicQosAsync(0, 1, false);
```

#### 连接池配置
```csharp
var factory = new ConnectionFactory
{
    UserName = "admin",
    Password = "admin123",
    HostName = "127.0.0.1",
    Port = 5672,
    VirtualHost = "/",
    AutomaticRecoveryEnabled = true,
    NetworkRecoveryInterval = TimeSpan.FromSeconds(10)
};
```

## 📊 监控和管理

### 1. 队列健康检查

```csharp
var status = await productionPlanService.GetRabbitMQQueueStatus();
Console.WriteLine($"连接状态: {status.IsConnected}");
Console.WriteLine($"分解队列消息数: {status.DecomposeQueueCount}");
Console.WriteLine($"总消息数: {status.TotalMessageCount}");
```

### 2. 错误处理

- **重试机制**: 失败的消息会自动重试，最多重试3次
- **指数退避**: 重试间隔按 2^n 分钟递增
- **死信队列**: 超过重试次数的消息进入死信队列
- **手动处理**: 可以从死信队列中手动恢复消息

### 3. 日志监控

```bash
# 查看应用程序日志
tail -f logs/application.log | grep -i rabbitmq

# 查看 RabbitMQ 日志
tail -f /var/log/rabbitmq/<EMAIL>
```

## 🎯 使用场景

### 1. 大批量分解

```csharp
// 适用于需要分解大量生产计划的场景
var batchInput = new BatchAsyncDecomposeRabbitMQInput
{
    PlanIds = largePlanIdList, // 1000+ 个计划
    Priority = 3,
    DecomposeType = DecomposeType.Full
};

var result = await productionPlanService.BatchDecomposeProductionPlanAsyncRabbitMQ(batchInput);
```

### 2. 高优先级处理

```csharp
// 紧急订单使用高优先级
var urgentInput = new AsyncDecomposeProductionPlanRabbitMQInput
{
    PlanId = urgentPlanId,
    Priority = 5, // 最高优先级
    MaxRetryCount = 5
};
```

### 3. 系统集成

```csharp
// 与其他系统集成，通过消息队列解耦
await ProductionPlanRabbitMQManager.PublishWorkOrderGenerationAsync(new WorkOrderGenerationRabbitMQMessage
{
    PlanId = planId,
    WorkOrderType = WorkOrderType.Material,
    MaterialId = materialId,
    RequiredQuantity = quantity
});
```

## 🔍 故障排查

### 常见问题

1. **RabbitMQ 连接失败**
   ```
   错误: None of the specified endpoints were reachable
   解决: 检查 RabbitMQ 服务状态和网络连接
   ```

2. **消息积压**
   ```
   现象: 队列中消息数量持续增长
   解决: 增加消费者实例或优化处理逻辑
   ```

3. **死信队列消息过多**
   ```
   现象: 死信队列中有大量消息
   解决: 检查业务逻辑错误，修复后重新处理
   ```

## 🚦 最佳实践

1. **合理设置优先级**: 紧急订单使用高优先级（4-5），常规订单使用中等优先级（2-3）
2. **监控队列状态**: 定期检查队列积压和死信队列
3. **错误处理**: 及时处理死信队列中的消息
4. **性能测试**: 在生产环境前进行压力测试
5. **备份策略**: 定期备份 RabbitMQ 数据和配置

## 📈 性能对比

| 指标 | 同步分解 | RabbitMQ 异步分解 |
|------|----------|-------------------|
| 处理能力 | 10-50个/分钟 | 500-1000个/分钟 |
| 响应时间 | 2-30秒 | 立即返回 |
| 可靠性 | 中等 | 高 |
| 扩展性 | 低 | 高 |
| 监控能力 | 有限 | 丰富 |

通过使用 RabbitMQ，您的生产计划分解系统将获得更好的性能、可靠性和可扩展性！🎉
