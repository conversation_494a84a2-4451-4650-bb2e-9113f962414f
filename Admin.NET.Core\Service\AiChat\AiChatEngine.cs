// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using LangChain.Providers.OpenAI;
using LangChain.Providers;
using LangChain.Chains.LLM;
using LangChain.Schema;

namespace Admin.NET.Core.Service;

/// <summary>
/// AI聊天引擎服务
/// </summary>
public class AiChatEngine : ITransient
{
    private readonly IOptions<AiChatOptions> _aiChatOptions;
    private readonly ILogger<AiChatEngine> _logger;
    private readonly SysCacheService _sysCacheService;

    public AiChatEngine(
        IOptions<AiChatOptions> aiChatOptions,
        ILogger<AiChatEngine> logger,
        SysCacheService sysCacheService)
    {
        _aiChatOptions = aiChatOptions;
        _logger = logger;
        _sysCacheService = sysCacheService;
    }

    /// <summary>
    /// AI回复响应
    /// </summary>
    public class AiResponse
    {
        public string Content { get; set; }
        public string ModelName { get; set; }
        public int ProcessTime { get; set; }
        public int TokenUsage { get; set; }
    }

    /// <summary>
    /// 获取AI回复
    /// </summary>
    public async Task<AiResponse> GetAiResponse(string prompt, string modelName = null)
    {
        var startTime = DateTime.Now;
        var options = _aiChatOptions.Value;

        try
        {
            if (string.IsNullOrWhiteSpace(options.OpenAiApiKey))
            {
                throw new InvalidOperationException("OpenAI API密钥未配置");
            }

            // 使用指定模型或默认模型
            var model = !string.IsNullOrWhiteSpace(modelName) ? modelName : options.DefaultModel;

            // 检查缓存
            string cacheKey = null;
            if (options.EnableCache)
            {
                cacheKey = $"ai_chat_cache:{prompt.GetHashCode()}:{model}";
                var cachedResponse = await _sysCacheService.GetAsync<string>(cacheKey);
                if (!string.IsNullOrWhiteSpace(cachedResponse))
                {
                    _logger.LogInformation("使用缓存的AI回复");
                    return new AiResponse
                    {
                        Content = cachedResponse,
                        ModelName = model,
                        ProcessTime = (int)(DateTime.Now - startTime).TotalMilliseconds,
                        TokenUsage = 0
                    };
                }
            }

            // 创建OpenAI提供者
            var provider = new OpenAiProvider(
                apiKey: options.OpenAiApiKey,
                baseUrl: options.OpenAiBaseUrl);

            // 创建聊天模型
            var chatModel = provider.GetChatModel(model);

            // 构建消息
            var messages = new List<Message>
            {
                new SystemMessage(options.SystemPrompt),
                new HumanMessage(prompt)
            };

            // 调用AI模型
            var response = await chatModel.GenerateAsync(messages);
            var content = response.Messages.LastOrDefault()?.Content ?? "抱歉，我无法生成回复。";

            var result = new AiResponse
            {
                Content = content,
                ModelName = model,
                ProcessTime = (int)(DateTime.Now - startTime).TotalMilliseconds,
                TokenUsage = response.Usage?.TotalTokens ?? 0
            };

            // 缓存结果
            if (options.EnableCache && !string.IsNullOrWhiteSpace(cacheKey))
            {
                await _sysCacheService.SetAsync(cacheKey, content, TimeSpan.FromMinutes(options.CacheExpirationMinutes));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AI回复生成失败 - 模型:{Model}, 提示词长度:{PromptLength}", 
                modelName ?? options.DefaultModel, prompt?.Length ?? 0);
            
            // 返回错误回复
            return new AiResponse
            {
                Content = "抱歉，我现在无法回复您的问题，请稍后再试。",
                ModelName = modelName ?? options.DefaultModel,
                ProcessTime = (int)(DateTime.Now - startTime).TotalMilliseconds,
                TokenUsage = 0
            };
        }
    }

    /// <summary>
    /// 构建AI提示词
    /// </summary>
    public async Task<string> BuildPrompt(string userMessage, List<ChatHistoryOutput> chatHistory, 
        List<KnowledgeSearchResult> relatedKnowledge)
    {
        var promptBuilder = new StringBuilder();
        var options = _aiChatOptions.Value;

        // 添加系统提示词
        promptBuilder.AppendLine("# 系统指令");
        promptBuilder.AppendLine(options.SystemPrompt);
        promptBuilder.AppendLine();

        // 添加相关知识库内容
        if (relatedKnowledge?.Any() == true)
        {
            promptBuilder.AppendLine("# 相关知识库");
            foreach (var knowledge in relatedKnowledge.Take(options.MaxKnowledgeRetrievalCount))
            {
                promptBuilder.AppendLine($"## {knowledge.Title}");
                promptBuilder.AppendLine(knowledge.Content);
                promptBuilder.AppendLine();
            }
        }

        // 添加对话历史
        if (chatHistory?.Any() == true)
        {
            promptBuilder.AppendLine("# 对话历史");
            var recentHistory = chatHistory
                .Where(h => h.Role != "system")
                .OrderBy(h => h.Sequence)
                .TakeLast(options.MaxHistoryLength)
                .ToList();

            foreach (var message in recentHistory)
            {
                var roleLabel = message.Role == "user" ? "用户" : "助手";
                promptBuilder.AppendLine($"{roleLabel}: {message.Content}");
            }
            promptBuilder.AppendLine();
        }

        // 添加当前用户消息
        promptBuilder.AppendLine("# 当前问题");
        promptBuilder.AppendLine($"用户: {userMessage}");
        promptBuilder.AppendLine();
        promptBuilder.AppendLine("请基于以上信息回答用户的问题。如果知识库中有相关信息，请优先使用知识库内容。回答要专业、准确、友好。");

        var prompt = promptBuilder.ToString();
        
        // 检查提示词长度
        if (prompt.Length > 8000) // 大概估算token限制
        {
            _logger.LogWarning("提示词过长，进行截断处理");
            // 简化处理：保留系统提示词和当前问题，减少历史记录
            return await BuildSimplifiedPrompt(userMessage, relatedKnowledge);
        }

        return prompt;
    }

    /// <summary>
    /// 构建简化的提示词
    /// </summary>
    private async Task<string> BuildSimplifiedPrompt(string userMessage, List<KnowledgeSearchResult> relatedKnowledge)
    {
        var promptBuilder = new StringBuilder();
        var options = _aiChatOptions.Value;

        promptBuilder.AppendLine("# 系统指令");
        promptBuilder.AppendLine(options.SystemPrompt);
        promptBuilder.AppendLine();

        // 只添加最相关的知识库内容
        if (relatedKnowledge?.Any() == true)
        {
            promptBuilder.AppendLine("# 相关知识");
            var topKnowledge = relatedKnowledge.Take(3).ToList(); // 只取前3个最相关的
            foreach (var knowledge in topKnowledge)
            {
                promptBuilder.AppendLine($"- {knowledge.Title}: {knowledge.Content.Substring(0, Math.Min(200, knowledge.Content.Length))}");
            }
            promptBuilder.AppendLine();
        }

        promptBuilder.AppendLine("# 用户问题");
        promptBuilder.AppendLine(userMessage);
        promptBuilder.AppendLine();
        promptBuilder.AppendLine("请基于以上信息简洁地回答用户的问题。");

        await Task.CompletedTask;
        return promptBuilder.ToString();
    }

    /// <summary>
    /// 验证API配置
    /// </summary>
    public async Task<bool> ValidateApiConfiguration()
    {
        try
        {
            var options = _aiChatOptions.Value;
            if (string.IsNullOrWhiteSpace(options.OpenAiApiKey))
            {
                return false;
            }

            // 发送一个简单的测试请求
            var testResponse = await GetAiResponse("Hello", options.DefaultModel);
            return !string.IsNullOrWhiteSpace(testResponse.Content);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "API配置验证失败");
            return false;
        }
    }
}
