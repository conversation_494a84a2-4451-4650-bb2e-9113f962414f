// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using Admin.NET.Core.EventBus.RabbitMQ;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using RabbitMQ.Client;

namespace Admin.NET.Application.Service.ProductionPlans;

/// <summary>
/// RabbitMQ 健康检查服务
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class RabbitMQHealthCheckController : ControllerBase
{
    private readonly ILogger<RabbitMQHealthCheckController> _logger;

    public RabbitMQHealthCheckController(ILogger<RabbitMQHealthCheckController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 检查 RabbitMQ 连接状态
    /// </summary>
    /// <returns></returns>
    [HttpGet("connection-status")]
    public async Task<IActionResult> CheckConnectionStatus()
    {
        try
        {
            var factory = ProductionPlanRabbitMQManager.GetConnectionFactory();
            using var connection = await factory.CreateConnectionAsync();
            
            var result = new
            {
                isConnected = connection.IsOpen,
                hostName = factory.HostName,
                port = factory.Port,
                userName = factory.UserName,
                virtualHost = factory.VirtualHost,
                message = connection.IsOpen ? "RabbitMQ 连接正常" : "RabbitMQ 连接失败",
                timestamp = DateTime.Now
            };

            if (connection.IsOpen)
            {
                await connection.CloseAsync();
                return Ok(result);
            }
            else
            {
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "RabbitMQ 连接检查失败");
            
            return BadRequest(new
            {
                isConnected = false,
                message = $"RabbitMQ 连接失败: {ex.Message}",
                error = ex.GetType().Name,
                timestamp = DateTime.Now,
                suggestions = new[]
                {
                    "1. 检查 RabbitMQ 服务是否启动",
                    "2. 验证连接配置是否正确",
                    "3. 检查网络连接和防火墙设置",
                    "4. 运行 'docker run -d --name rabbitmq -p 5672:5672 -p 15672:15672 rabbitmq:3-management'"
                }
            });
        }
    }

    /// <summary>
    /// 检查队列状态
    /// </summary>
    /// <returns></returns>
    [HttpGet("queue-status")]
    public async Task<IActionResult> CheckQueueStatus()
    {
        try
        {
            var status = await ProductionPlanRabbitMQManager.GetQueueStatusAsync();
            return Ok(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取队列状态失败");
            
            return BadRequest(new
            {
                error = ex.Message,
                timestamp = DateTime.Now,
                suggestions = new[]
                {
                    "1. 确保 RabbitMQ 服务正在运行",
                    "2. 检查应用程序是否有权限访问 RabbitMQ",
                    "3. 验证队列配置是否正确"
                }
            });
        }
    }

    /// <summary>
    /// 初始化队列和交换机
    /// </summary>
    /// <returns></returns>
    [HttpPost("initialize-queues")]
    public async Task<IActionResult> InitializeQueues()
    {
        try
        {
            var factory = ProductionPlanRabbitMQManager.GetConnectionFactory();
            using var connection = await factory.CreateConnectionAsync();
            using var channel = await connection.CreateChannelAsync();

            await ProductionPlanRabbitMQManager.InitializeQueuesAndExchangesAsync(channel);

            return Ok(new
            {
                success = true,
                message = "队列和交换机初始化成功",
                timestamp = DateTime.Now,
                queues = new[]
                {
                    ProductionPlanRabbitMQManager.DECOMPOSE_QUEUE,
                    ProductionPlanRabbitMQManager.STATUS_UPDATE_QUEUE,
                    ProductionPlanRabbitMQManager.WORK_ORDER_QUEUE,
                    ProductionPlanRabbitMQManager.DECOMPOSE_RETRY_QUEUE,
                    ProductionPlanRabbitMQManager.DECOMPOSE_DLQ
                },
                exchanges = new[]
                {
                    ProductionPlanRabbitMQManager.DECOMPOSE_EXCHANGE,
                    ProductionPlanRabbitMQManager.RETRY_EXCHANGE
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化队列和交换机失败");
            
            return BadRequest(new
            {
                success = false,
                message = $"初始化失败: {ex.Message}",
                timestamp = DateTime.Now
            });
        }
    }

    /// <summary>
    /// 测试发送消息
    /// </summary>
    /// <returns></returns>
    [HttpPost("test-message")]
    public async Task<IActionResult> TestMessage()
    {
        try
        {
            var testMessage = new ProductionPlanDecomposeRabbitMQMessage
            {
                PlanId = 999999, // 测试用的计划ID
                PlanCode = "TEST-PLAN-001",
                PlanName = "RabbitMQ 连接测试计划",
                Priority = 1,
                CreateUserId = 1,
                CreateUserName = "系统测试"
            };

            var result = await ProductionPlanRabbitMQManager.PublishDecomposeTaskAsync(testMessage);

            if (result)
            {
                return Ok(new
                {
                    success = true,
                    message = "测试消息发送成功",
                    messageId = testMessage.MessageId,
                    timestamp = DateTime.Now
                });
            }
            else
            {
                return BadRequest(new
                {
                    success = false,
                    message = "测试消息发送失败",
                    timestamp = DateTime.Now
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "测试消息发送失败");
            
            return BadRequest(new
            {
                success = false,
                message = $"测试消息发送失败: {ex.Message}",
                timestamp = DateTime.Now
            });
        }
    }

    /// <summary>
    /// 获取 RabbitMQ 配置信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("config")]
    public IActionResult GetConfig()
    {
        try
        {
            var eventBusOptions = App.GetConfig<EventBusOptions>("EventBus", true);
            
            return Ok(new
            {
                eventSourceType = eventBusOptions?.EventSourceType,
                rabbitMQ = new
                {
                    hostName = eventBusOptions?.RabbitMQ?.HostName,
                    port = eventBusOptions?.RabbitMQ?.Port,
                    userName = eventBusOptions?.RabbitMQ?.UserName,
                    // 不返回密码
                    passwordConfigured = !string.IsNullOrEmpty(eventBusOptions?.RabbitMQ?.Password)
                },
                timestamp = DateTime.Now
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配置信息失败");
            
            return BadRequest(new
            {
                error = ex.Message,
                timestamp = DateTime.Now
            });
        }
    }
}

/// <summary>
/// RabbitMQ 健康检查服务
/// </summary>
public class RabbitMQHealthCheckService
{
    private readonly ILogger<RabbitMQHealthCheckService> _logger;

    public RabbitMQHealthCheckService(ILogger<RabbitMQHealthCheckService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 检查 RabbitMQ 是否可用
    /// </summary>
    /// <returns></returns>
    public async Task<bool> IsRabbitMQAvailableAsync()
    {
        try
        {
            var factory = ProductionPlanRabbitMQManager.GetConnectionFactory();
            using var connection = await factory.CreateConnectionAsync();
            var isOpen = connection.IsOpen;
            if (isOpen)
            {
                await connection.CloseAsync();
            }
            return isOpen;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "RabbitMQ 不可用");
            return false;
        }
    }

    /// <summary>
    /// 等待 RabbitMQ 可用
    /// </summary>
    /// <param name="maxWaitSeconds">最大等待秒数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    public async Task<bool> WaitForRabbitMQAsync(int maxWaitSeconds = 30, CancellationToken cancellationToken = default)
    {
        var endTime = DateTime.Now.AddSeconds(maxWaitSeconds);
        
        while (DateTime.Now < endTime && !cancellationToken.IsCancellationRequested)
        {
            if (await IsRabbitMQAvailableAsync())
            {
                _logger.LogInformation("RabbitMQ 已可用");
                return true;
            }

            _logger.LogInformation("等待 RabbitMQ 可用...");
            await Task.Delay(2000, cancellationToken); // 等待2秒后重试
        }

        _logger.LogWarning("等待 RabbitMQ 超时");
        return false;
    }
}
