// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core.EventBus;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Admin.NET.Core.Startup;

/// <summary>
/// 生产计划队列服务启动配置
/// </summary>
[AppStartup(210)]
public class ProductionPlanQueueStartup : AppStartup
{
    public void ConfigureServices(IServiceCollection services)
    {
        // 注册其他相关服务
        services.AddSingleton<IProductionPlanQueueService, ProductionPlanQueueService>();
    }
}

/// <summary>
/// 生产计划队列服务接口
/// </summary>
public interface IProductionPlanQueueService
{
    /// <summary>
    /// 获取队列健康状态
    /// </summary>
    /// <returns></returns>
    Task<QueueHealthStatus> GetHealthStatusAsync();

    /// <summary>
    /// 清理过期消息
    /// </summary>
    /// <returns></returns>
    Task<int> CleanExpiredMessagesAsync();

    /// <summary>
    /// 重新处理失败的消息
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <returns></returns>
    Task<bool> ReprocessFailedMessageAsync(string messageId);
}

/// <summary>
/// 生产计划队列服务实现
/// </summary>
public class ProductionPlanQueueService : IProductionPlanQueueService
{
    private readonly ILogger<ProductionPlanQueueService> _logger;

    public ProductionPlanQueueService(ILogger<ProductionPlanQueueService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 获取队列健康状态
    /// </summary>
    /// <returns></returns>
    public async Task<QueueHealthStatus> GetHealthStatusAsync()
    {
        try
        {
            var queueStatus = await ProductionPlanDecomposeQueue.GetQueueStatusAsync();
            
            return new QueueHealthStatus
            {
                IsHealthy = string.IsNullOrEmpty(queueStatus.ErrorMessage),
                DecomposeQueueCount = queueStatus.DecomposeQueueCount,
                StatusUpdateQueueCount = queueStatus.StatusUpdateQueueCount,
                WorkOrderQueueCount = queueStatus.WorkOrderQueueCount,
                LastCheckTime = DateTime.Now,
                ErrorMessage = queueStatus.ErrorMessage
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取队列健康状态失败");
            return new QueueHealthStatus
            {
                IsHealthy = false,
                ErrorMessage = ex.Message,
                LastCheckTime = DateTime.Now
            };
        }
    }

    /// <summary>
    /// 清理过期消息
    /// </summary>
    /// <returns></returns>
    public async Task<int> CleanExpiredMessagesAsync()
    {
        try
        {
            // 这里可以实现清理过期消息的逻辑
            // 例如清理超过24小时的已完成消息
            _logger.LogInformation("开始清理过期消息");
            
            // TODO: 实现具体的清理逻辑
            await Task.Delay(100); // 占位符
            
            _logger.LogInformation("过期消息清理完成");
            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期消息失败");
            return -1;
        }
    }

    /// <summary>
    /// 重新处理失败的消息
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <returns></returns>
    public async Task<bool> ReprocessFailedMessageAsync(string messageId)
    {
        try
        {
            _logger.LogInformation("开始重新处理失败消息: MessageId={MessageId}", messageId);
            
            // TODO: 实现重新处理失败消息的逻辑
            await Task.Delay(100); // 占位符
            
            _logger.LogInformation("失败消息重新处理完成: MessageId={MessageId}", messageId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新处理失败消息时发生错误: MessageId={MessageId}", messageId);
            return false;
        }
    }
}

/// <summary>
/// 队列健康状态
/// </summary>
public class QueueHealthStatus
{
    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy { get; set; }

    /// <summary>
    /// 分解队列消息数量
    /// </summary>
    public int DecomposeQueueCount { get; set; }

    /// <summary>
    /// 状态更新队列消息数量
    /// </summary>
    public int StatusUpdateQueueCount { get; set; }

    /// <summary>
    /// 工单队列消息数量
    /// </summary>
    public int WorkOrderQueueCount { get; set; }

    /// <summary>
    /// 最后检查时间
    /// </summary>
    public DateTime LastCheckTime { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 总消息数量
    /// </summary>
    public int TotalMessageCount => DecomposeQueueCount + StatusUpdateQueueCount + WorkOrderQueueCount;

    /// <summary>
    /// 队列状态描述
    /// </summary>
    public string StatusDescription => IsHealthy 
        ? $"队列运行正常，总消息数: {TotalMessageCount}" 
        : $"队列异常: {ErrorMessage}";
}
