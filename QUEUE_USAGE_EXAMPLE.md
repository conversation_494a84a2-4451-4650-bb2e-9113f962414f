# 生产计划分解消息队列使用示例

## 🚀 快速开始

### 1. 异步分解单个生产计划

```csharp
// 在控制器或服务中注入 ProductionPlanService
[ApiController]
[Route("api/[controller]")]
public class ProductionPlanController : ControllerBase
{
    private readonly ProductionPlanService _productionPlanService;

    public ProductionPlanController(ProductionPlanService productionPlanService)
    {
        _productionPlanService = productionPlanService;
    }

    [HttpPost("async-decompose")]
    public async Task<IActionResult> AsyncDecompose([FromBody] AsyncDecomposeRequest request)
    {
        var input = new AsyncDecomposeProductionPlanInput
        {
            PlanId = request.PlanId,
            Priority = 5, // 高优先级
            DecomposeType = DecomposeType.Full, // 完整分解
            UseReliableQueue = true, // 使用可信队列
            MaxRetryCount = 3,
            TimeoutMinutes = 30,
            Options = new DecomposeOptions
            {
                GenerateMaterialWorkOrder = true,
                GenerateSubProductWorkOrder = true,
                GenerateMainBomWorkOrder = true,
                CheckInventory = false,
                AutoSchedule = false,
                ParallelProcessing = true
            }
        };

        var result = await _productionPlanService.DecomposeProductionPlanAsyncQueue(input);
        
        if (result.Success)
        {
            return Ok(new
            {
                success = true,
                message = "分解任务已提交",
                messageId = result.MessageId,
                estimatedTime = result.EstimatedProcessTime
            });
        }
        else
        {
            return BadRequest(new { success = false, message = result.Message });
        }
    }
}

public class AsyncDecomposeRequest
{
    public long PlanId { get; set; }
}
```

### 2. 批量异步分解

```csharp
[HttpPost("batch-async-decompose")]
public async Task<IActionResult> BatchAsyncDecompose([FromBody] BatchDecomposeRequest request)
{
    var input = new BatchAsyncDecomposeInput
    {
        PlanIds = request.PlanIds,
        Priority = 3,
        DecomposeType = DecomposeType.Full,
        UseReliableQueue = false, // 使用普通队列提高性能
        Options = new DecomposeOptions
        {
            GenerateMaterialWorkOrder = true,
            GenerateSubProductWorkOrder = true,
            ParallelProcessing = true
        }
    };

    var result = await _productionPlanService.BatchDecomposeProductionPlanAsync(input);
    
    return Ok(new
    {
        success = result.Success,
        message = result.Message,
        total = result.TotalCount,
        successCount = result.SuccessCount,
        failedCount = result.FailedCount,
        details = new
        {
            successTasks = result.SuccessResults.Select(r => new { r.PlanId, r.MessageId }),
            failedTasks = result.FailedResults.Select(r => new { r.PlanId, r.Message })
        }
    });
}

public class BatchDecomposeRequest
{
    public List<long> PlanIds { get; set; } = new();
}
```

### 3. 查询队列状态

```csharp
[HttpGet("queue-status")]
public async Task<IActionResult> GetQueueStatus()
{
    var status = await _productionPlanService.GetDecomposeQueueStatus();
    
    return Ok(new
    {
        decomposeQueue = status.DecomposeQueueCount,
        statusUpdateQueue = status.StatusUpdateQueueCount,
        workOrderQueue = status.WorkOrderQueueCount,
        lastUpdate = status.LastUpdateTime,
        hasError = !string.IsNullOrEmpty(status.ErrorMessage),
        errorMessage = status.ErrorMessage
    });
}
```

## 📊 监控和管理

### 1. 队列健康检查

```csharp
[ApiController]
[Route("api/[controller]")]
public class QueueManagementController : ControllerBase
{
    private readonly IProductionPlanQueueService _queueService;

    public QueueManagementController(IProductionPlanQueueService queueService)
    {
        _queueService = queueService;
    }

    [HttpGet("health")]
    public async Task<IActionResult> GetHealthStatus()
    {
        var health = await _queueService.GetHealthStatusAsync();
        
        return Ok(new
        {
            isHealthy = health.IsHealthy,
            queues = new
            {
                decompose = health.DecomposeQueueCount,
                statusUpdate = health.StatusUpdateQueueCount,
                workOrder = health.WorkOrderQueueCount
            },
            lastCheck = health.LastCheckTime,
            error = health.ErrorMessage
        });
    }

    [HttpPost("clean-expired")]
    public async Task<IActionResult> CleanExpiredMessages()
    {
        var cleanedCount = await _queueService.CleanExpiredMessagesAsync();
        
        return Ok(new
        {
            success = cleanedCount >= 0,
            cleanedCount = cleanedCount,
            message = cleanedCount >= 0 ? "清理完成" : "清理失败"
        });
    }

    [HttpPost("reprocess/{messageId}")]
    public async Task<IActionResult> ReprocessFailedMessage(string messageId)
    {
        var success = await _queueService.ReprocessFailedMessageAsync(messageId);
        
        return Ok(new
        {
            success = success,
            message = success ? "重新处理成功" : "重新处理失败"
        });
    }
}
```

## 🔧 配置示例

### 1. Redis 配置

在 `Admin.NET.Application/Configuration/Cache.json` 中：

```json
{
  "Cache": {
    "CacheType": "Redis",
    "Redis": {
      "Configuration": "server=127.0.0.1:6379;password=;db=5;",
      "Prefix": "adminnet_production_"
    }
  }
}
```

### 2. 分解选项配置

```csharp
// 完整分解配置
var fullDecomposeOptions = new DecomposeOptions
{
    GenerateMaterialWorkOrder = true,
    GenerateSubProductWorkOrder = true,
    GenerateMainBomWorkOrder = true,
    CheckInventory = true,
    AutoSchedule = false,
    ParallelProcessing = true,
    DecomposeDepth = -1, // 无限深度
    BatchSize = 100
};

// 仅物料分解配置
var materialOnlyOptions = new DecomposeOptions
{
    GenerateMaterialWorkOrder = true,
    GenerateSubProductWorkOrder = false,
    GenerateMainBomWorkOrder = false,
    CheckInventory = true,
    ParallelProcessing = true,
    DecomposeDepth = 1 // 只分解一层
};

// 高性能分解配置
var highPerformanceOptions = new DecomposeOptions
{
    GenerateMaterialWorkOrder = true,
    GenerateSubProductWorkOrder = true,
    GenerateMainBomWorkOrder = true,
    CheckInventory = false, // 跳过库存检查
    AutoSchedule = false,
    ParallelProcessing = true,
    BatchSize = 500 // 大批次处理
};
```

## 🎯 使用场景示例

### 1. 紧急订单处理

```csharp
// 紧急订单使用最高优先级和可信队列
var urgentInput = new AsyncDecomposeProductionPlanInput
{
    PlanId = urgentPlanId,
    Priority = 5, // 最高优先级
    DecomposeType = DecomposeType.Full,
    UseReliableQueue = true, // 确保不丢失
    MaxRetryCount = 5, // 增加重试次数
    TimeoutMinutes = 15, // 缩短超时时间
    Options = new DecomposeOptions
    {
        GenerateMaterialWorkOrder = true,
        GenerateSubProductWorkOrder = true,
        GenerateMainBomWorkOrder = true,
        CheckInventory = true,
        AutoSchedule = true, // 自动排产
        ParallelProcessing = true
    }
};
```

### 2. 大批量夜间处理

```csharp
// 夜间批量处理，优化性能
var batchInput = new BatchAsyncDecomposeInput
{
    PlanIds = largePlanIdList,
    Priority = 2, // 低优先级
    DecomposeType = DecomposeType.Full,
    UseReliableQueue = false, // 使用普通队列提高性能
    MaxRetryCount = 3,
    TimeoutMinutes = 60, // 延长超时时间
    Options = new DecomposeOptions
    {
        GenerateMaterialWorkOrder = true,
        GenerateSubProductWorkOrder = true,
        GenerateMainBomWorkOrder = true,
        CheckInventory = false, // 跳过库存检查提高性能
        AutoSchedule = false,
        ParallelProcessing = true,
        BatchSize = 1000 // 大批次处理
    }
};
```

### 3. 测试环境快速验证

```csharp
// 测试环境快速分解
var testInput = new AsyncDecomposeProductionPlanInput
{
    PlanId = testPlanId,
    Priority = 3,
    DecomposeType = DecomposeType.MaterialOnly, // 仅分解物料
    UseReliableQueue = false,
    MaxRetryCount = 1, // 减少重试
    TimeoutMinutes = 10,
    Options = new DecomposeOptions
    {
        GenerateMaterialWorkOrder = true,
        GenerateSubProductWorkOrder = false,
        GenerateMainBomWorkOrder = false,
        CheckInventory = false,
        AutoSchedule = false,
        ParallelProcessing = false, // 串行处理便于调试
        DecomposeDepth = 2 // 限制分解深度
    }
};
```

## 📈 性能优化建议

1. **队列选择**：
   - 普通队列：高性能，适合大批量处理
   - 可信队列：高可靠性，适合重要任务

2. **优先级设置**：
   - 5：紧急订单
   - 4：重要客户订单
   - 3：常规订单
   - 2：补充库存
   - 1：测试任务

3. **批次大小**：
   - 小批次（<100）：实时处理
   - 中批次（100-500）：平衡性能和响应
   - 大批次（>500）：夜间批处理

4. **并行处理**：
   - 启用并行处理可显著提高性能
   - 但会增加系统资源消耗
   - 根据服务器配置调整
