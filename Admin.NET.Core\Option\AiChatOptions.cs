// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core;

/// <summary>
/// AI聊天配置选项
/// </summary>
public sealed class AiChatOptions : IConfigurableOptions
{
    /// <summary>
    /// OpenAI API密钥
    /// </summary>
    public string OpenAiApiKey { get; set; }

    /// <summary>
    /// OpenAI API基础URL
    /// </summary>
    public string OpenAiBaseUrl { get; set; } = "https://api.openai.com/v1";

    /// <summary>
    /// 默认模型名称
    /// </summary>
    public string DefaultModel { get; set; } = "gpt-3.5-turbo";

    /// <summary>
    /// 最大Token数
    /// </summary>
    public int MaxTokens { get; set; } = 2000;

    /// <summary>
    /// 温度参数（0-2之间，控制回复的随机性）
    /// </summary>
    public double Temperature { get; set; } = 0.7;

    /// <summary>
    /// 系统提示词
    /// </summary>
    public string SystemPrompt { get; set; } = "你是一个专业的MES系统智能助手，专门帮助用户解决制造执行系统相关的问题。请用专业、友好的语气回答用户的问题。";

    /// <summary>
    /// 最大对话历史长度
    /// </summary>
    public int MaxHistoryLength { get; set; } = 20;

    /// <summary>
    /// 是否启用知识库检索
    /// </summary>
    public bool EnableKnowledgeRetrieval { get; set; } = true;

    /// <summary>
    /// 知识库检索相似度阈值
    /// </summary>
    public double KnowledgeSimilarityThreshold { get; set; } = 0.7;

    /// <summary>
    /// 最大知识库检索数量
    /// </summary>
    public int MaxKnowledgeRetrievalCount { get; set; } = 5;

    /// <summary>
    /// 是否启用流式响应
    /// </summary>
    public bool EnableStreaming { get; set; } = true;

    /// <summary>
    /// 请求超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 是否启用内容过滤
    /// </summary>
    public bool EnableContentFilter { get; set; } = true;

    /// <summary>
    /// 敏感词列表
    /// </summary>
    public List<string> SensitiveWords { get; set; } = new();

    /// <summary>
    /// 是否记录对话日志
    /// </summary>
    public bool EnableLogging { get; set; } = true;

    /// <summary>
    /// 是否启用缓存
    /// </summary>
    public bool EnableCache { get; set; } = true;

    /// <summary>
    /// 缓存过期时间（分钟）
    /// </summary>
    public int CacheExpirationMinutes { get; set; } = 60;
}
