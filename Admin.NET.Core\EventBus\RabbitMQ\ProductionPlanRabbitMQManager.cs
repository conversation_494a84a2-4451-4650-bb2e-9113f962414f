// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Microsoft.Extensions.Logging;
using RabbitMQ.Client;
using System.Text;
using System.Text.Json;

namespace Admin.NET.Core.EventBus.RabbitMQ;

/// <summary>
/// 生产计划 RabbitMQ 队列管理器
/// </summary>
public static class ProductionPlanRabbitMQManager
{
    private static readonly ILogger _logger = App.GetService<ILogger<object>>();

    // 队列名称常量
    public const string DECOMPOSE_QUEUE = "production_plan_decompose";
    public const string STATUS_UPDATE_QUEUE = "production_plan_status_update";
    public const string WORK_ORDER_QUEUE = "production_plan_work_order";
    public const string DECOMPOSE_RETRY_QUEUE = "production_plan_decompose_retry";
    public const string DECOMPOSE_DLQ = "production_plan_decompose_dlq"; // 死信队列

    // 交换机名称
    public const string DECOMPOSE_EXCHANGE = "production_plan_exchange";
    public const string RETRY_EXCHANGE = "production_plan_retry_exchange";

    // 路由键
    public const string DECOMPOSE_ROUTING_KEY = "decompose";
    public const string STATUS_UPDATE_ROUTING_KEY = "status_update";
    public const string WORK_ORDER_ROUTING_KEY = "work_order";
    public const string RETRY_ROUTING_KEY = "retry";

    /// <summary>
    /// 获取 RabbitMQ 连接工厂
    /// </summary>
    /// <returns></returns>
    public static ConnectionFactory GetConnectionFactory()
    {
        var eventBusOptions = App.GetConfig<EventBusOptions>("EventBus", true);
        
        return new ConnectionFactory
        {
            UserName = eventBusOptions.RabbitMQ.UserName,
            Password = eventBusOptions.RabbitMQ.Password,
            HostName = eventBusOptions.RabbitMQ.HostName,
            Port = eventBusOptions.RabbitMQ.Port,
            VirtualHost = "/",
            AutomaticRecoveryEnabled = true,
            NetworkRecoveryInterval = TimeSpan.FromSeconds(10)
        };
    }

    /// <summary>
    /// 初始化队列和交换机
    /// </summary>
    /// <param name="channel"></param>
    /// <returns></returns>
    public static async Task InitializeQueuesAndExchangesAsync(IChannel channel)
    {
        try
        {
            // 声明主交换机
            await channel.ExchangeDeclareAsync(
                exchange: DECOMPOSE_EXCHANGE,
                type: ExchangeType.Direct,
                durable: true,
                autoDelete: false);

            // 声明重试交换机
            await channel.ExchangeDeclareAsync(
                exchange: RETRY_EXCHANGE,
                type: ExchangeType.Direct,
                durable: true,
                autoDelete: false);

            // 声明死信队列
            await channel.QueueDeclareAsync(
                queue: DECOMPOSE_DLQ,
                durable: true,
                exclusive: false,
                autoDelete: false);

            // 声明分解队列（带死信队列配置）
            var decomposeQueueArgs = new Dictionary<string, object>
            {
                { "x-dead-letter-exchange", "" },
                { "x-dead-letter-routing-key", DECOMPOSE_DLQ },
                { "x-message-ttl", 1800000 } // 30分钟TTL
            };

            await channel.QueueDeclareAsync(
                queue: DECOMPOSE_QUEUE,
                durable: true,
                exclusive: false,
                autoDelete: false,
                arguments: decomposeQueueArgs);

            // 声明状态更新队列
            await channel.QueueDeclareAsync(
                queue: STATUS_UPDATE_QUEUE,
                durable: true,
                exclusive: false,
                autoDelete: false);

            // 声明工单生成队列
            await channel.QueueDeclareAsync(
                queue: WORK_ORDER_QUEUE,
                durable: true,
                exclusive: false,
                autoDelete: false);

            // 声明重试队列（带延迟配置）
            var retryQueueArgs = new Dictionary<string, object>
            {
                { "x-dead-letter-exchange", DECOMPOSE_EXCHANGE },
                { "x-dead-letter-routing-key", DECOMPOSE_ROUTING_KEY }
            };

            await channel.QueueDeclareAsync(
                queue: DECOMPOSE_RETRY_QUEUE,
                durable: true,
                exclusive: false,
                autoDelete: false,
                arguments: retryQueueArgs);

            // 绑定队列到交换机
            await channel.QueueBindAsync(DECOMPOSE_QUEUE, DECOMPOSE_EXCHANGE, DECOMPOSE_ROUTING_KEY);
            await channel.QueueBindAsync(STATUS_UPDATE_QUEUE, DECOMPOSE_EXCHANGE, STATUS_UPDATE_ROUTING_KEY);
            await channel.QueueBindAsync(WORK_ORDER_QUEUE, DECOMPOSE_EXCHANGE, WORK_ORDER_ROUTING_KEY);
            await channel.QueueBindAsync(DECOMPOSE_RETRY_QUEUE, RETRY_EXCHANGE, RETRY_ROUTING_KEY);

            _logger.LogInformation("RabbitMQ 队列和交换机初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化 RabbitMQ 队列和交换机失败");
            throw;
        }
    }

    /// <summary>
    /// 发布分解任务消息
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    public static async Task<bool> PublishDecomposeTaskAsync(ProductionPlanDecomposeRabbitMQMessage message)
    {
        try
        {
            var factory = GetConnectionFactory();
            using var connection = await factory.CreateConnectionAsync();
            using var channel = await connection.CreateChannelAsync();

            await InitializeQueuesAndExchangesAsync(channel);

            var body = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(message));
            var properties = new BasicProperties
            {
                Persistent = true,
                MessageId = message.MessageId,
                Timestamp = new AmqpTimestamp(DateTimeOffset.UtcNow.ToUnixTimeSeconds()),
                Priority = (byte)message.Priority,
                Headers = new Dictionary<string, object>
                {
                    { "retry_count", message.RetryCount },
                    { "max_retry_count", message.MaxRetryCount },
                    { "plan_id", message.PlanId.ToString() }
                }
            };

            await channel.BasicPublishAsync(
                exchange: DECOMPOSE_EXCHANGE,
                routingKey: DECOMPOSE_ROUTING_KEY,
                mandatory: true,
                basicProperties: properties,
                body: body);

            _logger.LogInformation("生产计划分解任务已发布到 RabbitMQ: PlanId={PlanId}, MessageId={MessageId}", 
                message.PlanId, message.MessageId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布生产计划分解任务到 RabbitMQ 失败: PlanId={PlanId}", message.PlanId);
            return false;
        }
    }

    /// <summary>
    /// 发布状态更新消息
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    public static async Task<bool> PublishStatusUpdateAsync(ProductionPlanStatusRabbitMQMessage message)
    {
        try
        {
            var factory = GetConnectionFactory();
            using var connection = await factory.CreateConnectionAsync();
            using var channel = await connection.CreateChannelAsync();

            await InitializeQueuesAndExchangesAsync(channel);

            var body = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(message));
            var properties = new BasicProperties
            {
                Persistent = true,
                MessageId = message.MessageId,
                Timestamp = new AmqpTimestamp(DateTimeOffset.UtcNow.ToUnixTimeSeconds())
            };

            await channel.BasicPublishAsync(
                exchange: DECOMPOSE_EXCHANGE,
                routingKey: STATUS_UPDATE_ROUTING_KEY,
                mandatory: true,
                basicProperties: properties,
                body: body);

            _logger.LogInformation("生产计划状态更新消息已发布到 RabbitMQ: PlanId={PlanId}, Status={Status}", 
                message.PlanId, message.NewStatus);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布生产计划状态更新到 RabbitMQ 失败: PlanId={PlanId}", message.PlanId);
            return false;
        }
    }

    /// <summary>
    /// 发布工单生成消息
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    public static async Task<bool> PublishWorkOrderGenerationAsync(WorkOrderGenerationRabbitMQMessage message)
    {
        try
        {
            var factory = GetConnectionFactory();
            using var connection = await factory.CreateConnectionAsync();
            using var channel = await connection.CreateChannelAsync();

            await InitializeQueuesAndExchangesAsync(channel);

            var body = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(message));
            var properties = new BasicProperties
            {
                Persistent = true,
                MessageId = message.MessageId,
                Timestamp = new AmqpTimestamp(DateTimeOffset.UtcNow.ToUnixTimeSeconds()),
                Priority = (byte)message.Priority
            };

            await channel.BasicPublishAsync(
                exchange: DECOMPOSE_EXCHANGE,
                routingKey: WORK_ORDER_ROUTING_KEY,
                mandatory: true,
                basicProperties: properties,
                body: body);

            _logger.LogInformation("工单生成消息已发布到 RabbitMQ: PlanId={PlanId}, WorkOrderType={WorkOrderType}", 
                message.PlanId, message.WorkOrderType);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布工单生成消息到 RabbitMQ 失败: PlanId={PlanId}", message.PlanId);
            return false;
        }
    }

    /// <summary>
    /// 发布重试消息
    /// </summary>
    /// <param name="message"></param>
    /// <param name="delaySeconds"></param>
    /// <returns></returns>
    public static async Task<bool> PublishRetryAsync(ProductionPlanDecomposeRabbitMQMessage message, int delaySeconds)
    {
        try
        {
            var factory = GetConnectionFactory();
            using var connection = await factory.CreateConnectionAsync();
            using var channel = await connection.CreateChannelAsync();

            await InitializeQueuesAndExchangesAsync(channel);

            message.RetryCount++;
            var body = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(message));
            var properties = new BasicProperties
            {
                Persistent = true,
                MessageId = message.MessageId,
                Timestamp = new AmqpTimestamp(DateTimeOffset.UtcNow.ToUnixTimeSeconds()),
                Expiration = (delaySeconds * 1000).ToString(), // 延迟时间（毫秒）
                Headers = new Dictionary<string, object>
                {
                    { "retry_count", message.RetryCount },
                    { "max_retry_count", message.MaxRetryCount },
                    { "plan_id", message.PlanId.ToString() }
                }
            };

            await channel.BasicPublishAsync(
                exchange: RETRY_EXCHANGE,
                routingKey: RETRY_ROUTING_KEY,
                mandatory: true,
                basicProperties: properties,
                body: body);

            _logger.LogInformation("生产计划分解重试消息已发布到 RabbitMQ: PlanId={PlanId}, RetryCount={RetryCount}, DelaySeconds={DelaySeconds}", 
                message.PlanId, message.RetryCount, delaySeconds);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布生产计划分解重试消息到 RabbitMQ 失败: PlanId={PlanId}", message.PlanId);
            return false;
        }
    }

    /// <summary>
    /// 获取队列状态信息
    /// </summary>
    /// <returns></returns>
    public static async Task<RabbitMQQueueStatus> GetQueueStatusAsync()
    {
        try
        {
            var factory = GetConnectionFactory();
            using var connection = await factory.CreateConnectionAsync();
            using var channel = await connection.CreateChannelAsync();

            // 注意：RabbitMQ.Client 7.x 版本中，QueueDeclarePassive 方法可能不可用
            // 这里使用简化的状态检查
            return new RabbitMQQueueStatus
            {
                IsConnected = connection.IsOpen,
                LastUpdateTime = DateTime.Now,
                ErrorMessage = connection.IsOpen ? null : "RabbitMQ 连接已断开"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取 RabbitMQ 队列状态失败");
            return new RabbitMQQueueStatus
            {
                IsConnected = false,
                LastUpdateTime = DateTime.Now,
                ErrorMessage = ex.Message
            };
        }
    }
}

/// <summary>
/// RabbitMQ 队列状态信息
/// </summary>
public class RabbitMQQueueStatus
{
    /// <summary>
    /// 是否连接
    /// </summary>
    public bool IsConnected { get; set; }

    /// <summary>
    /// 分解队列消息数量
    /// </summary>
    public uint DecomposeQueueCount { get; set; }

    /// <summary>
    /// 状态更新队列消息数量
    /// </summary>
    public uint StatusUpdateQueueCount { get; set; }

    /// <summary>
    /// 工单队列消息数量
    /// </summary>
    public uint WorkOrderQueueCount { get; set; }

    /// <summary>
    /// 重试队列消息数量
    /// </summary>
    public uint RetryQueueCount { get; set; }

    /// <summary>
    /// 死信队列消息数量
    /// </summary>
    public uint DeadLetterQueueCount { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdateTime { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 总消息数量
    /// </summary>
    public uint TotalMessageCount => DecomposeQueueCount + StatusUpdateQueueCount + WorkOrderQueueCount + RetryQueueCount;
}
