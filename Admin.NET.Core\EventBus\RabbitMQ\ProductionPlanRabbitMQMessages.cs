// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core.Enum;

namespace Admin.NET.Core.EventBus.RabbitMQ;

/// <summary>
/// 生产计划分解消息
/// </summary>
public class ProductionPlanDecomposeRabbitMQMessage
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 生产计划ID
    /// </summary>
    public long PlanId { get; set; }

    /// <summary>
    /// 计划编号
    /// </summary>
    public string? PlanCode { get; set; }

    /// <summary>
    /// 计划名称
    /// </summary>
    public string? PlanName { get; set; }

    /// <summary>
    /// BOM ID
    /// </summary>
    public long? BomId { get; set; }

    /// <summary>
    /// 计划数量
    /// </summary>
    public int PlanQuantity { get; set; }

    /// <summary>
    /// 优先级 (1-5, 5最高)
    /// </summary>
    public int Priority { get; set; } = 3;

    /// <summary>
    /// 分解类型
    /// </summary>
    public DecomposeType DecomposeType { get; set; } = DecomposeType.Full;

    /// <summary>
    /// 分解选项
    /// </summary>
    public DecomposeOptions? Options { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;

    /// <summary>
    /// 超时时间（分钟）
    /// </summary>
    public int TimeoutMinutes { get; set; } = 30;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建用户ID
    /// </summary>
    public long? CreateUserId { get; set; }

    /// <summary>
    /// 创建用户名
    /// </summary>
    public string? CreateUserName { get; set; }

    /// <summary>
    /// 扩展数据
    /// </summary>
    public Dictionary<string, object>? ExtendData { get; set; }
}

/// <summary>
/// 生产计划状态更新消息
/// </summary>
public class ProductionPlanStatusRabbitMQMessage
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 生产计划ID
    /// </summary>
    public long PlanId { get; set; }

    /// <summary>
    /// 原状态
    /// </summary>
    public ProductionPlanStatusEnum OldStatus { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    public ProductionPlanStatusEnum NewStatus { get; set; }

    /// <summary>
    /// 状态变更原因
    /// </summary>
    public string? Reason { get; set; }

    /// <summary>
    /// 操作用户ID
    /// </summary>
    public long? OperatorUserId { get; set; }

    /// <summary>
    /// 操作用户名
    /// </summary>
    public string? OperatorUserName { get; set; }

    /// <summary>
    /// 操作时间
    /// </summary>
    public DateTime OperateTime { get; set; } = DateTime.Now;
}

/// <summary>
/// 工单生成消息
/// </summary>
public class WorkOrderGenerationRabbitMQMessage
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 生产计划ID
    /// </summary>
    public long PlanId { get; set; }

    /// <summary>
    /// BOM ID
    /// </summary>
    public long? BomId { get; set; }

    /// <summary>
    /// 工单类型
    /// </summary>
    public WorkOrderType WorkOrderType { get; set; }

    /// <summary>
    /// 物料ID
    /// </summary>
    public long? MaterialId { get; set; }

    /// <summary>
    /// 物料编号
    /// </summary>
    public string? MaterialCode { get; set; }

    /// <summary>
    /// 物料名称
    /// </summary>
    public string? MaterialName { get; set; }

    /// <summary>
    /// 需求数量
    /// </summary>
    public decimal RequiredQuantity { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; } = 3;

    /// <summary>
    /// 计划开始时间
    /// </summary>
    public DateTime? PlannedStartTime { get; set; }

    /// <summary>
    /// 计划完成时间
    /// </summary>
    public DateTime? PlannedEndTime { get; set; }

    /// <summary>
    /// 工单编号前缀
    /// </summary>
    public string? WorkOrderPrefix { get; set; }

    /// <summary>
    /// 批次号
    /// </summary>
    public string? BatchNumber { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建用户ID
    /// </summary>
    public long? CreateUserId { get; set; }

    /// <summary>
    /// 扩展数据
    /// </summary>
    public Dictionary<string, object>? ExtendData { get; set; }
}

/// <summary>
/// 分解类型枚举
/// </summary>
public enum DecomposeType
{
    /// <summary>
    /// 完整分解
    /// </summary>
    Full = 1,

    /// <summary>
    /// 仅分解物料
    /// </summary>
    MaterialOnly = 2,

    /// <summary>
    /// 仅分解半成品
    /// </summary>
    SubProductOnly = 3,

    /// <summary>
    /// 自定义分解
    /// </summary>
    Custom = 4
}

/// <summary>
/// 工单类型枚举
/// </summary>
public enum WorkOrderType
{
    /// <summary>
    /// 主BOM工单
    /// </summary>
    MainBom = 1,

    /// <summary>
    /// 物料工单
    /// </summary>
    Material = 2,

    /// <summary>
    /// 半成品工单
    /// </summary>
    SubProduct = 3
}

/// <summary>
/// 分解选项
/// </summary>
public class DecomposeOptions
{
    /// <summary>
    /// 生成物料工单
    /// </summary>
    public bool GenerateMaterialWorkOrder { get; set; } = true;

    /// <summary>
    /// 生成半成品工单
    /// </summary>
    public bool GenerateSubProductWorkOrder { get; set; } = true;

    /// <summary>
    /// 生成主BOM工单
    /// </summary>
    public bool GenerateMainBomWorkOrder { get; set; } = true;

    /// <summary>
    /// 检查库存
    /// </summary>
    public bool CheckInventory { get; set; } = false;

    /// <summary>
    /// 自动排产
    /// </summary>
    public bool AutoSchedule { get; set; } = false;

    /// <summary>
    /// 并行处理
    /// </summary>
    public bool ParallelProcessing { get; set; } = true;

    /// <summary>
    /// 分解深度 (-1表示无限深度)
    /// </summary>
    public int DecomposeDepth { get; set; } = -1;

    /// <summary>
    /// 批处理大小
    /// </summary>
    public int BatchSize { get; set; } = 100;
}
