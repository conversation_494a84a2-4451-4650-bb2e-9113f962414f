// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using NewLife.Caching;
using NewLife.Caching.Queues;
using NewLife.Log;
using Microsoft.Extensions.Logging;

namespace Admin.NET.Core.EventBus;

/// <summary>
/// 生产计划分解消息队列
/// </summary>
public class ProductionPlanDecomposeQueue
{
    private static readonly ICacheProvider _cacheProvider = App.GetService<ICacheProvider>();
    private static readonly ILogger<ProductionPlanDecomposeQueue> _logger = App.GetService<ILogger<ProductionPlanDecomposeQueue>>();

    /// <summary>
    /// 生产计划分解任务队列主题
    /// </summary>
    public const string DECOMPOSE_TOPIC = "production_plan_decompose";

    /// <summary>
    /// 生产计划分解状态更新队列主题
    /// </summary>
    public const string STATUS_UPDATE_TOPIC = "production_plan_status_update";

    /// <summary>
    /// 工单生成队列主题
    /// </summary>
    public const string WORK_ORDER_TOPIC = "work_order_generation";

    /// <summary>
    /// 获取生产计划分解任务队列
    /// </summary>
    /// <returns></returns>
    public static IProducerConsumer<ProductionPlanDecomposeMessage> GetDecomposeQueue()
    {
        return RedisQueue.GetQueue<ProductionPlanDecomposeMessage>(DECOMPOSE_TOPIC, "decompose_group");
    }

    /// <summary>
    /// 获取生产计划分解可信队列（需要确认）
    /// </summary>
    /// <returns></returns>
    public static RedisReliableQueue<ProductionPlanDecomposeMessage> GetReliableDecomposeQueue()
    {
        return RedisQueue.GetRedisReliableQueue<ProductionPlanDecomposeMessage>(DECOMPOSE_TOPIC);
    }

    /// <summary>
    /// 获取状态更新队列
    /// </summary>
    /// <returns></returns>
    public static IProducerConsumer<ProductionPlanStatusMessage> GetStatusUpdateQueue()
    {
        return RedisQueue.GetQueue<ProductionPlanStatusMessage>(STATUS_UPDATE_TOPIC, "status_group");
    }

    /// <summary>
    /// 获取工单生成队列
    /// </summary>
    /// <returns></returns>
    public static IProducerConsumer<WorkOrderGenerationMessage> GetWorkOrderQueue()
    {
        return RedisQueue.GetQueue<WorkOrderGenerationMessage>(WORK_ORDER_TOPIC, "workorder_group");
    }

    /// <summary>
    /// 获取延迟队列（用于重试机制）
    /// </summary>
    /// <returns></returns>
    public static RedisDelayQueue<ProductionPlanDecomposeMessage> GetDelayQueue()
    {
        return RedisQueue.GetDelayQueue<ProductionPlanDecomposeMessage>($"{DECOMPOSE_TOPIC}_delay");
    }

    /// <summary>
    /// 发布生产计划分解任务
    /// </summary>
    /// <param name="message">分解消息</param>
    /// <returns></returns>
    public static async Task<bool> PublishDecomposeTaskAsync(ProductionPlanDecomposeMessage message)
    {
        try
        {
            var queue = GetDecomposeQueue();
            await Task.Run(() => queue.Add(new[] { message }));

            _logger.LogInformation("生产计划分解任务已发布到队列: PlanId={PlanId}, Priority={Priority}",
                message.PlanId, message.Priority);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布生产计划分解任务失败: PlanId={PlanId}", message.PlanId);
            return false;
        }
    }

    /// <summary>
    /// 发布生产计划分解任务（可信队列）
    /// </summary>
    /// <param name="message">分解消息</param>
    /// <returns></returns>
    public static async Task<bool> PublishReliableDecomposeTaskAsync(ProductionPlanDecomposeMessage message)
    {
        try
        {
            var queue = GetReliableDecomposeQueue();
            await Task.Run(() => queue.Add(message));
            
            _logger.LogInformation("生产计划分解任务已发布到可信队列: PlanId={PlanId}, MessageId={MessageId}", 
                message.PlanId, message.MessageId);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布生产计划分解任务到可信队列失败: PlanId={PlanId}", message.PlanId);
            return false;
        }
    }

    /// <summary>
    /// 发布状态更新消息
    /// </summary>
    /// <param name="message">状态更新消息</param>
    /// <returns></returns>
    public static async Task<bool> PublishStatusUpdateAsync(ProductionPlanStatusMessage message)
    {
        try
        {
            var queue = GetStatusUpdateQueue();
            await Task.Run(() => queue.Add(new[] { message }));

            _logger.LogInformation("生产计划状态更新消息已发布: PlanId={PlanId}, Status={Status}",
                message.PlanId, message.NewStatus);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布生产计划状态更新消息失败: PlanId={PlanId}", message.PlanId);
            return false;
        }
    }

    /// <summary>
    /// 发布工单生成消息
    /// </summary>
    /// <param name="message">工单生成消息</param>
    /// <returns></returns>
    public static async Task<bool> PublishWorkOrderGenerationAsync(WorkOrderGenerationMessage message)
    {
        try
        {
            var queue = GetWorkOrderQueue();
            await Task.Run(() => queue.Add(new[] { message }));

            _logger.LogInformation("工单生成消息已发布: PlanId={PlanId}, WorkOrderType={WorkOrderType}",
                message.PlanId, message.WorkOrderType);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布工单生成消息失败: PlanId={PlanId}", message.PlanId);
            return false;
        }
    }

    /// <summary>
    /// 发布延迟重试任务
    /// </summary>
    /// <param name="message">分解消息</param>
    /// <param name="delaySeconds">延迟秒数</param>
    /// <returns></returns>
    public static async Task<bool> PublishDelayRetryAsync(ProductionPlanDecomposeMessage message, int delaySeconds = 60)
    {
        try
        {
            var delayQueue = GetDelayQueue();
            await Task.Run(() => delayQueue.Add(message, delaySeconds));

            _logger.LogInformation("生产计划分解重试任务已发布到延迟队列: PlanId={PlanId}, DelaySeconds={DelaySeconds}",
                message.PlanId, delaySeconds);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布生产计划分解重试任务失败: PlanId={PlanId}", message.PlanId);
            return false;
        }
    }

    /// <summary>
    /// 确认消息处理完成（用于可信队列）
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <returns></returns>
    public static async Task<bool> AckMessageAsync(string messageId)
    {
        try
        {
            var queue = GetReliableDecomposeQueue();
            await Task.Run(() => queue.Acknowledge(messageId));

            _logger.LogInformation("消息确认成功: MessageId={MessageId}", messageId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "消息确认失败: MessageId={MessageId}", messageId);
            return false;
        }
    }

    /// <summary>
    /// 获取队列状态信息
    /// </summary>
    /// <returns></returns>
    public static async Task<QueueStatusInfo> GetQueueStatusAsync()
    {
        try
        {
            var decomposeQueue = GetDecomposeQueue();
            var statusQueue = GetStatusUpdateQueue();
            var workOrderQueue = GetWorkOrderQueue();

            return await Task.FromResult(new QueueStatusInfo
            {
                DecomposeQueueCount = decomposeQueue?.Count ?? 0,
                StatusUpdateQueueCount = statusQueue?.Count ?? 0,
                WorkOrderQueueCount = workOrderQueue?.Count ?? 0,
                LastUpdateTime = DateTime.Now
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取队列状态信息失败");
            return new QueueStatusInfo
            {
                DecomposeQueueCount = -1,
                StatusUpdateQueueCount = -1,
                WorkOrderQueueCount = -1,
                LastUpdateTime = DateTime.Now,
                ErrorMessage = ex.Message
            };
        }
    }
}

/// <summary>
/// 队列状态信息
/// </summary>
public class QueueStatusInfo
{
    /// <summary>
    /// 分解队列消息数量
    /// </summary>
    public int DecomposeQueueCount { get; set; }

    /// <summary>
    /// 状态更新队列消息数量
    /// </summary>
    public int StatusUpdateQueueCount { get; set; }

    /// <summary>
    /// 工单队列消息数量
    /// </summary>
    public int WorkOrderQueueCount { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdateTime { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
}
