# 生产计划分解消息队列系统

## 📋 概述

本系统为生产计划分解提供了基于Redis的异步消息队列解决方案，支持高并发、可靠性和可扩展性的生产计划分解处理。

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端/API      │    │   消息队列       │    │   消费者服务     │
│                 │    │                 │    │                 │
│ 提交分解任务     │───▶│ Redis Queue     │───▶│ 异步处理分解     │
│ 查询队列状态     │    │ - 分解队列       │    │ - 状态更新       │
│ 批量分解        │    │ - 状态队列       │    │ - 工单生成       │
│                 │    │ - 工单队列       │    │ - 重试机制       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 核心功能

### 1. 异步分解处理
- **同步分解**: 传统的同步分解方式，适合小批量处理
- **异步分解**: 基于消息队列的异步分解，适合大批量和高并发场景
- **批量分解**: 支持一次性提交多个生产计划进行分解

### 2. 消息队列类型
- **普通队列**: 适用于一般的分解任务
- **可信队列**: 需要确认机制，确保消息不丢失
- **延迟队列**: 支持重试机制和延迟处理

### 3. 多种工单类型
- **主BOM工单**: 主产品的生产工单
- **物料工单**: 原材料的采购/准备工单
- **半成品工单**: 子BOM的生产工单

## 📝 API接口

### 异步分解生产计划
```http
POST /api/productionPlan/decomposeProductionPlanAsyncQueue
Content-Type: application/json

{
  "planId": 123,
  "priority": 5,
  "decomposeType": 1,
  "useReliableQueue": true,
  "maxRetryCount": 3,
  "timeoutMinutes": 30,
  "options": {
    "generateMaterialWorkOrder": true,
    "generateSubProductWorkOrder": true,
    "generateMainBomWorkOrder": true,
    "checkInventory": false,
    "autoSchedule": false,
    "parallelProcessing": true
  }
}
```

### 批量异步分解
```http
POST /api/productionPlan/batchDecomposeProductionPlanAsync
Content-Type: application/json

{
  "planIds": [123, 124, 125],
  "priority": 3,
  "decomposeType": 1,
  "useReliableQueue": false,
  "options": {
    "generateMaterialWorkOrder": true,
    "generateSubProductWorkOrder": true
  }
}
```

### 获取队列状态
```http
GET /api/productionPlan/getDecomposeQueueStatus
```

## 🔧 配置说明

### 队列配置
在 `Admin.NET.Application/Configuration/Cache.json` 中配置Redis连接：

```json
{
  "Cache": {
    "CacheType": "Redis",
    "Redis": {
      "Configuration": "server=127.0.0.1:6379;password=;db=5;",
      "Prefix": "adminnet_"
    }
  }
}
```

### 分解选项配置
```csharp
public class DecomposeOptions
{
    public bool GenerateMaterialWorkOrder { get; set; } = true;     // 生成物料工单
    public bool GenerateSubProductWorkOrder { get; set; } = true;   // 生成半成品工单
    public bool GenerateMainBomWorkOrder { get; set; } = true;      // 生成主BOM工单
    public bool CheckInventory { get; set; } = false;               // 检查库存
    public bool AutoSchedule { get; set; } = false;                 // 自动排产
    public bool ParallelProcessing { get; set; } = true;            // 并行处理
    public int DecomposeDepth { get; set; } = -1;                   // 分解深度(-1无限)
}
```

## 📊 监控和管理

### 队列状态监控
```csharp
var queueStatus = await productionPlanService.GetDecomposeQueueStatus();
Console.WriteLine($"分解队列消息数: {queueStatus.DecomposeQueueCount}");
Console.WriteLine($"状态更新队列消息数: {queueStatus.StatusUpdateQueueCount}");
Console.WriteLine($"工单队列消息数: {queueStatus.WorkOrderQueueCount}");
```

### 健康检查
系统提供队列健康检查功能，可以监控：
- 队列连接状态
- 消息积压情况
- 处理性能指标
- 错误率统计

## 🔄 重试机制

### 自动重试
- **指数退避**: 重试间隔按 2^n 分钟递增
- **最大重试次数**: 默认3次，可配置
- **失败处理**: 超过重试次数后标记为失败状态

### 手动重试
```csharp
// 重新处理失败的消息
var success = await queueService.ReprocessFailedMessageAsync(messageId);
```

## 🎯 使用场景

### 1. 大批量分解
当需要分解大量生产计划时，使用异步队列可以：
- 避免接口超时
- 提高系统响应性
- 支持并发处理

### 2. 高峰期处理
在生产高峰期，异步队列可以：
- 平滑处理负载
- 避免系统过载
- 保证服务稳定性

### 3. 复杂BOM分解
对于多层级、复杂的BOM结构：
- 支持深度分解
- 并行处理子BOM
- 优化处理性能

## 🛠️ 部署和运维

### 服务部署
1. 确保Redis服务正常运行
2. 配置正确的Redis连接字符串
3. 启动应用程序（消费者服务会自动启动）

### 性能调优
- **队列大小**: 根据内存情况调整队列容量
- **消费者数量**: 可以部署多个消费者实例
- **批处理大小**: 调整批量处理的数量

### 故障处理
- **消息丢失**: 使用可信队列确保消息不丢失
- **处理失败**: 查看日志，使用重试机制
- **队列积压**: 增加消费者实例或优化处理逻辑

## 📈 性能指标

### 处理能力
- **单个分解**: 通常2-5分钟完成
- **批量分解**: 支持并行处理，线性扩展
- **队列吞吐**: 每秒可处理数百个消息

### 资源消耗
- **内存**: 每个消息约1-2KB
- **CPU**: 主要消耗在数据库操作
- **网络**: Redis通信开销较小

## 🔍 故障排查

### 常见问题
1. **队列连接失败**: 检查Redis配置和网络连接
2. **消息处理慢**: 检查数据库性能和BOM复杂度
3. **重试次数过多**: 检查业务逻辑和数据完整性

### 日志查看
```bash
# 查看消费者日志
tail -f logs/consumer.log

# 查看队列状态日志
tail -f logs/queue-status.log
```

## 🚦 最佳实践

1. **合理设置优先级**: 紧急订单使用高优先级
2. **监控队列状态**: 定期检查队列积压情况
3. **错误处理**: 及时处理失败的分解任务
4. **性能测试**: 在生产环境前进行压力测试
5. **备份策略**: 定期备份重要的分解数据
